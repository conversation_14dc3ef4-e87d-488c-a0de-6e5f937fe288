CREATE TABLE `travel_plans` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `email_address` varchar(255) NOT NULL,
  `email_consent` tinyint(1) NOT NULL,
  `telephone_number` varchar(255) NOT NULL,
  `house_name_or_number` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `contact_method` varchar(255) DEFAULT NULL,
  `destination_country` varchar(255) DEFAULT NULL,
  `destination_region` varchar(255) DEFAULT NULL,
  `num_adults` int(10) DEFAULT NULL,
  `num_children` int(10) DEFAULT NULL,
  `travel_date` varchar(255) DEFAULT NULL,
  `duration` varchar(255) DEFAULT NULL,
  `min_budget` varchar(255) DEFAULT NULL,
  `max_budget` varchar(255) DEFAULT NULL,
  `other_info` text CHARACTER SET utf8mb4,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=latin1;

/* The code below is just for reference */

/* Add admin permissions for Travel Plans */
-- INSERT INTO `acos` (`id`, `parent_id`, `model`, `foreign_key`, `alias`, `lft`, `rght`)
-- VALUES
-- 	(426, 1, NULL, NULL, 'TravelPlans', 831, 840),
-- 	(427, 426, NULL, NULL, 'webadmin_add', 832, 833),
-- 	(431, 426, NULL, NULL, 'webadmin_index', 834, 835),
-- 	(430, 426, NULL, NULL, 'webadmin_edit', 836, 837),
-- 	(432, 426, NULL, NULL, 'webadmin_delete', 838, 839);

-- /* Update controllers permission to also cover TravelPlans */
-- UPDATE `acos` SET `rght` = 841 WHERE `alias` = 'controllers';

-- /* Allow the Admin group to access the Travel Plans */
-- INSERT INTO `aros_acos` (`id`, `aro_id`, `aco_id`, `_create`, `_read`, `_update`, `_delete`)
-- VALUES
-- 	(58, 1, 426, '1', '1', '1', '1');
