const { VueLoaderPlugin } = require('vue-loader');
const path = require('path');

module.exports = {
  entry: './app/webroot/js/site/vue/components.js',
  output: {
    path: path.resolve(__dirname, './app/webroot/js/site'),
    filename: 'components.js',
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.js$/,
        loader: 'babel-loader',
        exclude: /node_modules/
      },
    ]
  },
  resolve: {
    extensions: ['.js', '.vue'],
  },
  plugins: [
    new VueLoaderPlugin()
  ]
}
