const gulp = require('gulp');
const gulpLoadPlugins = require('gulp-load-plugins');
const del = require('del');
const wiredep = require('wiredep').stream;
const sass = require('gulp-ruby-sass');
const criticalcss = require('criticalcss');
const _ = require('lodash');
const fs = require('fs');
const webpack = require('webpack');
const webpackConfig = require('./webpack.config.js');


const $ = gulpLoadPlugins();
const webroot = 'app/webroot';
const layouts = 'app/view/layouts';

gulp.task('styles', () => {
    return sass(`${webroot}/css/sass/*.scss`, {
        style: 'expanded',
        precision: 10,
        loadPath: [
            webroot,
            `${webroot}/bower_components/support-for/sass`,
            `${webroot}/bower_components/normalize-scss/sass`,
            'node_modules'
        ],
        compass: true,
        sourcemap: true,
    })
    .on('error', sass.logError)
    .pipe($.plumber())
    .pipe($.sourcemaps.init())
    .pipe($.autoprefixer({browsers: ['> 1%', 'last 2 versions', 'Firefox ESR']}))
    .pipe($.sourcemaps.write('maps', {
        includeContent: false,
        sourceRoot: `${webroot}/css`,
    }))
    .pipe(gulp.dest(`${webroot}/css`));
});

gulp.task('new_styles', () => {
  return gulp.src(`${webroot}/css/style.css`)
  .pipe($.cssnano({
    zindex: false,
  }))
  .pipe(gulp.dest(`${webroot}/css/build`));
});

gulp.task('scripts', () => {
    return gulp.src(`${webroot}/js/site/src/{,*/}*.js`)
        .pipe($.plumber())
        .pipe($.sourcemaps.init())
        // .pipe($.babel())
        .pipe($.sourcemaps.write('.'))
        .pipe(gulp.dest(`${webroot}/js/site`));
});

gulp.task('vue', (done) => {
  webpack(webpackConfig).run((err, stats) => {
      if(err) {
        console.log('Error', err);
      } else {
        console.log(stats.toString());
      }
      done();
  });
});

function lint(files, options) {
    return () => {
        return gulp.src(files)
        // .pipe(reload({stream: true, once: true}))
        .pipe($.eslint(options))
        .pipe($.eslint.format())
        .pipe($.if(!false, $.eslint.failAfterError()));
    };
}

gulp.task('lint', lint(`${webroot}/js/site/src/{,*/}*.js`));

gulp.task('html', gulp.series(gulp.parallel(gulp.series('styles', 'new_styles'), 'scripts', 'vue'), html = () => {
    return gulp.src('app/views/layouts/dev/*.ctp')
        .pipe($.useref({
            searchPath: [`${webroot}`],
        }))
        .pipe($.if('*.js', $.uglify({
            mangle: false,
        })))
        .pipe($.replace('/img/', '//resources.bon-voyage.co.uk/img/'))
        .pipe($.if('*.css', $.cssnano({
            zindex: false,
        })))
        .pipe($.if('*.ctp', $.htmlmin({collapseWhitespace: false})))
        .pipe(gulp.dest(file => {
            if (file.path.indexOf('.ctp') !== -1 || !file.path) {
              return 'app/views/layouts/prod';
            }
            return webroot;
        }));
}));

gulp.task('images', () => {
    return gulp.src(`${webroot}/img/site/**/*`)
    .pipe($.cache($.imagemin({
        progressive: true,
        interlaced: true,
        // don't remove IDs from SVGs, they are often used
        // as hooks for embedding and styling
        svgoPlugins: [{cleanupIDs: false}]
    })))
    .pipe(gulp.dest(`${webroot}/img/site`));
});

gulp.task('clean', del.bind(null, ['.tmp', 'dist']));

gulp.task('serve', gulp.series('styles', 'new_styles', 'scripts', 'vue', serve = () => {
    gulp.watch([
        'app/views/**/*.ctp',
        'app/webroot/blog/app/themes/bonvoyage/view/**/*.twig',
        `${webroot}/img/**/*`
    ]);

    gulp.watch(`${webroot}/css/sass/**/*.scss`, gulp.series('styles', 'new_styles'));
    gulp.watch(`${webroot}/js/site/src/*.js`, gulp.series('scripts'));
    gulp.watch(`${webroot}/js/site/vue/components.js`, gulp.series('vue'));
    gulp.watch('bower.json', gulp.series('wiredep'));
}));

// inject bower components
gulp.task('wiredep', () => {
    gulp.src(`${webroot}/css/sass/*.scss`)
    .pipe(wiredep({
        ignorePath: /^(\.\.\/)+/
    }))
    .pipe(gulp.dest(`${webroot}/css/sass`));
});

gulp.task('critical', () => {
    const urls = {
        home: 'http://localhost:8888/',
        destinations: 'http://localhost:8888/destinations/usa_holidays',
        holidays: 'http://localhost:8888/holidays',
        holidaysView: 'http://localhost:8888/holidays/fly_drive_holidays',
        page: 'http://localhost:8888/page/holiday_information',
        hot: 'http://localhost:8888/whats_hot',
        testimonials: 'http://localhost:8888/page/testimonials',
        faqs: 'http://localhost:8888/faqs',
        contact: 'http://localhost:8888/contact_us',
        campaigns: 'http://localhost:8888/campaigns',
        quote: 'http://localhost:8888/quote_requests/add',
        travelPlan: 'http://localhost:8888/travel_plans/add',
        search: 'http://localhost:8888/search/las-vegas',
    };

    criticalcss.getRules(`${webroot}/css/build/screen.css`, (err, output) => {
        if (err) {
            throw new Error(err);
        }
        const options = {
            width: 768,
            height: 1024,
            buffer: 800 * 1024,
            ignoreConsole: true,
            rules: JSON.parse(output),
        };
        _.forEach(urls, function (url, key) {
            criticalcss.findCritical(url, options, (err, css) => {
                if (err) {
                    throw new Error(err);
                }
                fs.writeFileSync(`${webroot}/css/critical/${key}.css`, css);
            });
        });
    });
});

// gulp.task('build', gulp.series('html', 'critical'));
gulp.task('build', gulp.series('html'));

gulp.task('default', gulp.series('build'));
