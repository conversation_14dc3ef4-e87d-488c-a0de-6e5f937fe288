{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "0095aef031fa3d682f86ffccca83dc93", "packages": [{"name": "altorouter/altorouter", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/dannyvankooten/AltoRouter.git", "reference": "09d9d946c546bae6d22a7654cdb3b825ffda54b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dannyvankooten/AltoRouter/zipball/09d9d946c546bae6d22a7654cdb3b825ffda54b4", "reference": "09d9d946c546bae6d22a7654cdb3b825ffda54b4", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"classmap": ["AltoRouter.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dannyvankooten.com/"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/koenpunt"}, {"name": "niahoo", "homepage": "https://github.com/niahoo"}], "description": "A lightning fast router for PHP", "homepage": "https://github.com/dannyvankooten/AltoRouter", "keywords": ["lightweight", "router", "routing"], "time": "2014-04-16T09:44:40+00:00"}, {"name": "asm89/twig-cache-extension", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/asm89/twig-cache-extension.git", "reference": "13787226956ec766f4770722082288097aebaaf3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/twig-cache-extension/zipball/13787226956ec766f4770722082288097aebaaf3", "reference": "13787226956ec766f4770722082288097aebaaf3", "shasum": ""}, "require": {"php": ">=5.3.2", "twig/twig": "^1.0|^2.0"}, "require-dev": {"doctrine/cache": "~1.0", "phpunit/phpunit": "^5.0 || ^4.8.10"}, "suggest": {"psr/cache-implementation": "To make use of PSR-6 cache implementation via PsrCacheAdapter."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cache fragments of templates directly within Twig.", "homepage": "https://github.com/asm89/twig-cache-extension", "keywords": ["cache", "extension", "twig"], "abandoned": "twig/cache-extension", "time": "2020-01-01T20:47:37+00:00"}, {"name": "ass/xmlsecurity", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/aschamberger/XmlSecurity.git", "reference": "c8976519ebbf6e4d953cd781d09df44b7f65fbb8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aschamberger/XmlSecurity/zipball/c8976519ebbf6e4d953cd781d09df44b7f65fbb8", "reference": "c8976519ebbf6e4d953cd781d09df44b7f65fbb8", "shasum": ""}, "require": {"ext-openssl": "*", "lib-openssl": ">=0.9.0", "php": ">=5.3.0"}, "require-dev": {"satooshi/php-coveralls": "dev-master"}, "suggest": {"ext-mcrypt": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"ass\\XmlSecurity": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The XmlSecurity library is written in PHP for working with XML Encryption and Signatures", "homepage": "https://github.com/aschamberger/XmlSecurity", "keywords": ["encryption", "security", "signature", "xml"], "time": "2015-05-31T10:10:35+00:00"}, {"name": "besimple/soap", "version": "v0.2.6", "source": {"type": "git", "url": "https://github.com/BeSimple/BeSimpleSoap.git", "reference": "fb56d51bd8c70ae6d7092f90fd7fec23520477df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BeSimple/BeSimpleSoap/zipball/fb56d51bd8c70ae6d7092f90fd7fec23520477df", "reference": "fb56d51bd8c70ae6d7092f90fd7fec23520477df", "shasum": ""}, "require": {"ass/xmlsecurity": "~1.0", "ext-curl": "*", "ext-soap": "*", "php": ">=5.3.0", "symfony/framework-bundle": "~2.0", "symfony/twig-bundle": "~2.0", "zendframework/zend-mime": "2.1.*"}, "replace": {"besimple/soap-bundle": "self.version", "besimple/soap-client": "self.version", "besimple/soap-common": "self.version", "besimple/soap-server": "self.version", "besimple/soap-wsdl": "self.version"}, "require-dev": {"ext-mcrypt": "*", "mikey179/vfsstream": "~1.0", "symfony/filesystem": "~2.0", "symfony/process": "~2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.2-dev"}}, "autoload": {"psr-0": {"BeSimple\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Build and consume SOAP and WSDL based web services", "homepage": "http://besim.pl", "keywords": ["soap"], "time": "2015-03-27T15:07:42+00:00"}, {"name": "cakephp/cakephp", "version": "dev-php7.2-migration", "source": {"type": "git", "url": "https://github.com/tomb1n0/cakephp.git", "reference": "091c2ea7a54ef1cf3e45becf669912c73933c205"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tomb1n0/cakephp/zipball/091c2ea7a54ef1cf3e45becf669912c73933c205", "reference": "091c2ea7a54ef1cf3e45becf669912c73933c205", "shasum": ""}, "type": "library", "license": ["MIT"], "description": "The CakePHP framework", "homepage": "https://cakephp.org", "keywords": ["conventions over configuration", "dry", "form", "framework", "mvc", "orm", "psr-7", "rapid-development", "validation"], "support": {"source": "https://github.com/mozmorris/cakephp/tree/php7.2-migration"}, "time": "2019-03-07T21:27:00+00:00"}, {"name": "composer/installers", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/d20a64ed3c94748397ff5973488761b22f6d3f19", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "tastyigniter", "typo3", "wordpress", "yawik", "zend", "zikula"], "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-09-13T08:19:44+00:00"}, {"name": "composer/semver", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "84c47f3d8901440403217afc120683c7385aecb8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/84c47f3d8901440403217afc120683c7385aecb8", "reference": "84c47f3d8901440403217afc120683c7385aecb8", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5", "phpunit/phpunit-mock-objects": "2.3.0 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2016-03-30T13:16:03+00:00"}, {"name": "composer/xdebug-handler", "version": "1.4.6", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "f27e06cd9675801df441b3656569b328e04aa37c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/f27e06cd9675801df441b3656569b328e04aa37c", "reference": "f27e06cd9675801df441b3656569b328e04aa37c", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-03-25T17:01:18+00:00"}, {"name": "doctrine/annotations", "version": "1.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "648b0343343565c4a056bfc8392201385e8d89f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/648b0343343565c4a056bfc8392201385e8d89f0", "reference": "648b0343343565c4a056bfc8392201385e8d89f0", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2", "vimeo/psalm": "^4.10"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2022-07-02T10:48:51+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "felixfbecker/advanced-json-rpc", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/felixfbecker/php-advanced-json-rpc.git", "reference": "b5f37dbff9a8ad360ca341f3240dc1c168b45447"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/felixfbecker/php-advanced-json-rpc/zipball/b5f37dbff9a8ad360ca341f3240dc1c168b45447", "reference": "b5f37dbff9a8ad360ca341f3240dc1c168b45447", "shasum": ""}, "require": {"netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "php": "^7.1 || ^8.0", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5.0.0"}, "require-dev": {"phpunit/phpunit": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"AdvancedJsonRpc\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A more advanced JSONRPC implementation", "time": "2021-06-11T22:34:44+00:00"}, {"name": "guzzlehttp/guzzle", "version": "5.3.4", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "b87eda7a7162f95574032da17e9323c9899cb6b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/b87eda7a7162f95574032da17e9323c9899cb6b2", "reference": "b87eda7a7162f95574032da17e9323c9899cb6b2", "shasum": ""}, "require": {"guzzlehttp/ringphp": "^1.1", "php": ">=5.4.0", "react/promise": "^2.2"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0"}, "type": "library", "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library and framework for building RESTful web service clients", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2019-10-30T09:32:00+00:00"}, {"name": "guzzlehttp/ringphp", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/guzzle/RingPHP.git", "reference": "5e2a174052995663dd68e6b5ad838afd47dd615b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/RingPHP/zipball/5e2a174052995663dd68e6b5ad838afd47dd615b", "reference": "5e2a174052995663dd68e6b5ad838afd47dd615b", "shasum": ""}, "require": {"guzzlehttp/streams": "~3.0", "php": ">=5.4.0", "react/promise": "~2.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple API and specification that abstracts away the details of HTTP into a single PHP function.", "abandoned": true, "time": "2018-07-31T13:22:33+00:00"}, {"name": "guzzlehttp/streams", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/guzzle/streams.git", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/streams/zipball/47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple abstraction over streams of data", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "abandoned": true, "time": "2014-10-12T19:18:40+00:00"}, {"name": "jarednova/timber", "version": "0.22.6", "source": {"type": "git", "url": "https://github.com/timber/timber.git", "reference": "06e7faded55fcbcedb34d753bc93987cab4ee978"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/timber/timber/zipball/06e7faded55fcbcedb34d753bc93987cab4ee978", "reference": "06e7faded55fcbcedb34d753bc93987cab4ee978", "shasum": ""}, "require": {"asm89/twig-cache-extension": "~1.0", "composer/installers": "~1.0", "php": ">=5.3.0", "twig/twig": "1.*", "upstatement/routes": "0.3"}, "require-dev": {"jarednova/markdowndocs": "dev-master", "phpdocumentor/phpdocumentor": "2.*", "phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "1.0.*", "wp-cli/wp-cli": "*", "wpackagist-plugin/advanced-custom-fields": "4.*"}, "type": "wordpress-plugin", "autoload": {"classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://upstatement.com"}], "description": "Plugin to write WordPress themes w Object-Oriented Code and the Twig Template Engine", "homepage": "http://timber.upstatement.com", "keywords": ["templating", "themes", "timber", "twig"], "abandoned": "timber/timber", "time": "2016-03-13T15:44:19+00:00"}, {"name": "jenssegers/proxy", "version": "v2.2.1", "source": {"type": "git", "url": "https://github.com/jenssegers/php-proxy.git", "reference": "7dd08bac110eb5b78ab89b970ac90548e11db511"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/php-proxy/zipball/7dd08bac110eb5b78ab89b970ac90548e11db511", "reference": "7dd08bac110eb5b78ab89b970ac90548e11db511", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~4.0|~5.0", "symfony/http-foundation": "~2.6"}, "require-dev": {"mockery/mockery": "~0.9", "phpunit/phpunit": "~4.4", "satooshi/php-coveralls": "~0.6"}, "type": "library", "autoload": {"psr-4": {"Proxy\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "http://jenssegers.be"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.rebuy.de"}], "description": "Proxy library that forwards requests to the desired url and returns the response.", "homepage": "https://github.com/jenssegers/php-proxy", "keywords": ["proxy"], "time": "2015-04-05T09:04:31+00:00"}, {"name": "johnpbloch/wordpress", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/johnpbloch/wordpress.git", "reference": "6c3d7d1a4ddb3b27e6f3ca4b2018b0150d7221b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnpbloch/wordpress/zipball/6c3d7d1a4ddb3b27e6f3ca4b2018b0150d7221b7", "reference": "6c3d7d1a4ddb3b27e6f3ca4b2018b0150d7221b7", "shasum": ""}, "require": {"johnpbloch/wordpress-core": "6.0.2", "johnpbloch/wordpress-core-installer": "^1.0 || ^2.0", "php": ">=5.6.20"}, "type": "package", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "WordPress Community", "homepage": "https://wordpress.org/about/"}], "description": "WordPress is open source software you can use to create a beautiful website, blog, or app.", "homepage": "https://wordpress.org/", "keywords": ["blog", "cms", "wordpress"], "time": "2022-08-30T17:46:21+00:00"}, {"name": "johnpbloch/wordpress-core", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/johnpbloch/wordpress-core.git", "reference": "aa7b334880fd8158abe64469e71570bd8815f273"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnpbloch/wordpress-core/zipball/aa7b334880fd8158abe64469e71570bd8815f273", "reference": "aa7b334880fd8158abe64469e71570bd8815f273", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6.20"}, "provide": {"wordpress/core-implementation": "6.0.2"}, "type": "wordpress-core", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "WordPress Community", "homepage": "https://wordpress.org/about/"}], "description": "WordPress is open source software you can use to create a beautiful website, blog, or app.", "homepage": "https://wordpress.org/", "keywords": ["blog", "cms", "wordpress"], "time": "2022-08-30T17:46:17+00:00"}, {"name": "johnpbloch/wordpress-core-installer", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/johnpbloch/wordpress-core-installer.git", "reference": "237faae9a60a4a2e1d45dce1a5836ffa616de63e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnpbloch/wordpress-core-installer/zipball/237faae9a60a4a2e1d45dce1a5836ffa616de63e", "reference": "237faae9a60a4a2e1d45dce1a5836ffa616de63e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.6.0"}, "conflict": {"composer/installers": "<1.0.6"}, "require-dev": {"composer/composer": "^1.0 || ^2.0", "phpunit/phpunit": ">=5.7.27"}, "type": "composer-plugin", "extra": {"class": "johnpbloch\\Composer\\WordPressCorePlugin"}, "autoload": {"psr-0": {"johnpbloch\\Composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A custom installer to handle deploying WordPress with composer", "keywords": ["wordpress"], "time": "2020-04-16T21:44:57+00:00"}, {"name": "microsoft/tolerant-php-parser", "version": "v0.0.17", "source": {"type": "git", "url": "https://github.com/Microsoft/tolerant-php-parser.git", "reference": "89386de8dec9c004c8ea832692e236c92f34b542"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Microsoft/tolerant-php-parser/zipball/89386de8dec9c004c8ea832692e236c92f34b542", "reference": "89386de8dec9c004c8ea832692e236c92f34b542", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.4"}, "type": "library", "autoload": {"psr-4": {"Microsoft\\PhpParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Tolerant PHP-to-AST parser designed for IDE usage scenarios", "time": "2019-03-09T19:24:59+00:00"}, {"name": "mustache/mustache", "version": "v2.14.2", "source": {"type": "git", "url": "https://github.com/bobthecow/mustache.php.git", "reference": "e62b7c3849d22ec55f3ec425507bf7968193a6cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/mustache.php/zipball/e62b7c3849d22ec55f3ec425507bf7968193a6cb", "reference": "e62b7c3849d22ec55f3ec425507bf7968193a6cb", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "~1.11", "phpunit/phpunit": "~3.7|~4.0|~5.0"}, "type": "library", "autoload": {"psr-0": {"Mustache": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "A Mustache implementation in PHP.", "homepage": "https://github.com/bobthecow/mustache.php", "keywords": ["mustache", "templating"], "time": "2022-08-23T13:07:01+00:00"}, {"name": "netresearch/jsonmapper", "version": "v4.0.0", "source": {"type": "git", "url": "https://github.com/cweiske/jsonmapper.git", "reference": "8bbc021a8edb2e4a7ea2f8ad4fa9ec9dce2fcb8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweiske/jsonmapper/zipball/8bbc021a8edb2e4a7ea2f8ad4fa9ec9dce2fcb8d", "reference": "8bbc021a8edb2e4a7ea2f8ad4fa9ec9dce2fcb8d", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~7.5 || ~8.0 || ~9.0", "squizlabs/php_codesniffer": "~3.5"}, "type": "library", "autoload": {"psr-0": {"JsonMapper": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/cweiske/jsonmapper/", "role": "Developer"}], "description": "Map nested JSON structures onto PHP classes", "time": "2020-12-01T19:48:11+00:00"}, {"name": "nikic/php-parser", "version": "v4.15.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "0ef6c55a3f47f89d7a374e6f835197a0b5fcf900"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/0ef6c55a3f47f89d7a374e6f835197a0b5fcf900", "reference": "0ef6c55a3f47f89d7a374e6f835197a0b5fcf900", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2022-09-04T07:30:47+00:00"}, {"name": "oscarotero/env", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/oscarotero/env.git", "reference": "4ab45ce5c1f2c62549208426bfa20a3d5fa008c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/oscarotero/env/zipball/4ab45ce5c1f2c62549208426bfa20a3d5fa008c6", "reference": "4ab45ce5c1f2c62549208426bfa20a3d5fa008c6", "shasum": ""}, "require": {"ext-ctype": "*", "php": ">=5.2"}, "type": "library", "autoload": {"psr-0": {"Env": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "Simple library to consume environment variables", "homepage": "https://github.com/oscarotero/env", "keywords": ["env"], "time": "2019-04-03T18:28:43+00:00"}, {"name": "phan/phan", "version": "1.3.5", "source": {"type": "git", "url": "https://github.com/phan/phan.git", "reference": "f872949b366d84bd9ce79d8b0ea991edbe258377"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phan/phan/zipball/f872949b366d84bd9ce79d8b0ea991edbe258377", "reference": "f872949b366d84bd9ce79d8b0ea991edbe258377", "shasum": ""}, "require": {"composer/semver": "^1.4", "composer/xdebug-handler": "^1.3.2", "ext-filter": "*", "ext-json": "*", "felixfbecker/advanced-json-rpc": "^3.0.3", "microsoft/tolerant-php-parser": "0.0.17", "php": "^7.0.0", "sabre/event": "^5.0", "symfony/console": "^2.3|^3.0|~4.0", "symfony/polyfill-mbstring": "^1.11.0"}, "require-dev": {"brianium/paratest": "^1.1", "phpunit/phpunit": "^6.3.0"}, "suggest": {"ext-ast": "Needed for parsing ASTs (unless --use-fallback-parser is used). 1.0.1+ is recommended, php-ast ^0.1.5|^1.0.0 is needed.", "ext-igbinary": "Improves performance of polyfill when ext-ast is unavailable", "ext-tokenizer": "Needed for non-AST support and file/line-based suppressions."}, "bin": ["phan", "phan_client", "tocheckstyle"], "type": "project", "autoload": {"psr-4": {"Phan\\": "src/Phan"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "A static analyzer for PHP", "keywords": ["analyzer", "php", "static"], "time": "2019-05-23T00:25:02+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "77a32518733312af16a44300404e945338981de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/77a32518733312af16a44300404e945338981de3", "reference": "77a32518733312af16a44300404e945338981de3", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2022-03-15T21:29:03+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2021-05-03T11:20:27+00:00"}, {"name": "psy/psysh", "version": "v0.10.12", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "a0d9981aa07ecfcbea28e4bfa868031cca121e7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/a0d9981aa07ecfcbea28e4bfa868031cca121e7d", "reference": "a0d9981aa07ecfcbea28e4bfa868031cca121e7d", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3", "php": "^8.0 || ^7.0 || ^5.5.9", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "3.17.*"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-main": "0.10.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "time": "2021-11-30T14:05:36+00:00"}, {"name": "react/promise", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/234f8fd1023c9158e2314fa9d7d0e6a83db42910", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "funding": [{"url": "https://github.com/WyriHaximus", "type": "github"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-11T10:27:51+00:00"}, {"name": "rmccue/requests", "version": "v1.8.1", "source": {"type": "git", "url": "https://github.com/WordPress/Requests.git", "reference": "82e6936366eac3af4d836c18b9d8c31028fe4cd5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/Requests/zipball/82e6936366eac3af4d836c18b9d8c31028fe4cd5", "reference": "82e6936366eac3af4d836c18b9d8c31028fe4cd5", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5", "requests/test-server": "dev-master", "squizlabs/php_codesniffer": "^3.5", "wp-coding-standards/wpcs": "^2.0"}, "type": "library", "autoload": {"psr-0": {"Requests": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "http://ryanmccue.info"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "http://github.com/WordPress/Requests", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "time": "2021-06-04T09:56:25+00:00"}, {"name": "sabre/event", "version": "5.1.4", "source": {"type": "git", "url": "https://github.com/sabre-io/event.git", "reference": "d7da22897125d34d7eddf7977758191c06a74497"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/event/zipball/d7da22897125d34d7eddf7977758191c06a74497", "reference": "d7da22897125d34d7eddf7977758191c06a74497", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.0"}, "type": "library", "autoload": {"files": ["lib/coroutine.php", "lib/Loop/functions.php", "lib/Promise/functions.php"], "psr-4": {"Sabre\\Event\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "sabre/event is a library for lightweight event-based programming", "homepage": "http://sabre.io/event/", "keywords": ["EventEmitter", "async", "coroutine", "eventloop", "events", "hooks", "plugin", "promise", "reactor", "signal"], "time": "2021-11-04T06:51:17+00:00"}, {"name": "symfony/asset", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "f844899f663285ce819c041237777140e0c061c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/f844899f663285ce819c041237777140e0c061c3", "reference": "f844899f663285ce819c041237777140e0c061c3", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"symfony/http-foundation": "~2.8|~3.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Asset Component", "homepage": "https://symfony.com", "time": "2016-07-01T15:14:41+00:00"}, {"name": "symfony/class-loader", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/class-loader.git", "reference": "00c66ca2de5a9a367706826338df721529a07ca8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/class-loader/zipball/00c66ca2de5a9a367706826338df721529a07ca8", "reference": "00c66ca2de5a9a367706826338df721529a07ca8", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"symfony/finder": "~2.8|~3.0", "symfony/polyfill-apcu": "~1.1"}, "suggest": {"symfony/polyfill-apcu": "For using ApcClassLoader on HHVM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ClassLoader\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ClassLoader Component", "homepage": "https://symfony.com", "time": "2016-07-10T08:04:44+00:00"}, {"name": "symfony/config", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "7dd5f5040dc04c118d057fb5886563963eb70011"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/7dd5f5040dc04c118d057fb5886563963eb70011", "reference": "7dd5f5040dc04c118d057fb5886563963eb70011", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/filesystem": "~2.3|~3.0.0", "symfony/polyfill-ctype": "~1.8"}, "require-dev": {"symfony/yaml": "~2.7|~3.0.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2018-11-26T09:38:12+00:00"}, {"name": "symfony/console", "version": "v3.2.14", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "eced439413608647aeff243038a33ea246b2b33a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/eced439413608647aeff243038a33ea246b2b33a", "reference": "eced439413608647aeff243038a33ea246b2b33a", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/debug": "~2.8|~3.0", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/filesystem": "~2.8|~3.0", "symfony/process": "~2.8|~3.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/filesystem": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2017-07-29T21:27:41+00:00"}, {"name": "symfony/debug", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "74251c8d50dd3be7c4ce0c7b862497cdc641a5d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/74251c8d50dd3be7c4ce0c7b862497cdc641a5d0", "reference": "74251c8d50dd3be7c4ce0c7b862497cdc641a5d0", "shasum": ""}, "require": {"php": ">=5.3.9", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/class-loader": "~2.2|~3.0.0", "symfony/http-kernel": "~2.3.24|~2.5.9|^2.6.2|~3.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "abandoned": "symfony/error-handler", "time": "2018-11-11T11:18:13+00:00"}, {"name": "symfony/dependency-injection", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "c306198fee8f872a8f5f031e6e4f6f83086992d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/c306198fee8f872a8f5f031e6e4f6f83086992d8", "reference": "c306198fee8f872a8f5f031e6e4f6f83086992d8", "shasum": ""}, "require": {"php": ">=5.3.9"}, "conflict": {"symfony/expression-language": "<2.6"}, "require-dev": {"symfony/config": "~2.2|~3.0.0", "symfony/expression-language": "~2.6|~3.0.0", "symfony/yaml": "~2.3.42|~2.7.14|~2.8.7|~3.0.7"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "time": "2019-04-16T11:33:46+00:00"}, {"name": "symfony/event-dispatcher", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "54da3ff63dec3c9c0e32ec3f95a7d94ef64baa00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/54da3ff63dec3c9c0e32ec3f95a7d94ef64baa00", "reference": "54da3ff63dec3c9c0e32ec3f95a7d94ef64baa00", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0", "symfony/dependency-injection": "~2.8|~3.0", "symfony/expression-language": "~2.8|~3.0", "symfony/stopwatch": "~2.8|~3.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2016-07-19T10:44:15+00:00"}, {"name": "symfony/filesystem", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b2da5009d9bacbd91d83486aa1f44c793a8c380d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b2da5009d9bacbd91d83486aa1f44c793a8c380d", "reference": "b2da5009d9bacbd91d83486aa1f44c793a8c380d", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2016-07-20T05:43:46+00:00"}, {"name": "symfony/finder", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "3eb4e64c6145ef8b92adefb618a74ebdde9e3fe9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/3eb4e64c6145ef8b92adefb618a74ebdde9e3fe9", "reference": "3eb4e64c6145ef8b92adefb618a74ebdde9e3fe9", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2016-06-29T05:40:00+00:00"}, {"name": "symfony/framework-bundle", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "07b6056e3a84861fa8a54c33f70b189cf18a1aad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/07b6056e3a84861fa8a54c33f70b189cf18a1aad", "reference": "07b6056e3a84861fa8a54c33f70b189cf18a1aad", "shasum": ""}, "require": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "ext-xml": "*", "php": ">=5.3.9", "symfony/asset": "~2.7|~3.0.0", "symfony/class-loader": "~2.1|~3.0.0", "symfony/config": "~2.8", "symfony/dependency-injection": "~2.8.41", "symfony/event-dispatcher": "~2.8|~3.0.0", "symfony/filesystem": "~2.3|~3.0.0", "symfony/finder": "^2.0.5|~3.0.0", "symfony/http-foundation": "~2.7.36|^2.8.29", "symfony/http-kernel": "^2.8.22", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^2.8.17", "symfony/security-core": "^2.8.41|^3.3.17", "symfony/security-csrf": "^2.8.31|^3.3.13", "symfony/stopwatch": "~2.3|~3.0.0", "symfony/templating": "~2.7|~3.0.0", "symfony/translation": "~2.8"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "require-dev": {"phpdocumentor/reflection": "^1.0.7", "sensio/framework-extra-bundle": "^3.0.2", "symfony/browser-kit": "~2.4|~3.0.0", "symfony/console": "~2.8.19|~3.2.7", "symfony/css-selector": "^2.0.5|~3.0.0", "symfony/dom-crawler": "^2.0.5|~3.0.0", "symfony/expression-language": "~2.6|~3.0.0", "symfony/form": "^2.8.19", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^2.0.5|~3.0.0", "symfony/property-info": "~2.8|~3.0.0", "symfony/validator": "~2.5|~3.0.0", "symfony/yaml": "^2.0.5|~3.0.0", "twig/twig": "~1.34|~2.4"}, "suggest": {"symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/process": "For using the server:run, server:start, server:stop, and server:status commands", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony FrameworkBundle", "homepage": "https://symfony.com", "time": "2019-04-16T10:01:35+00:00"}, {"name": "symfony/http-foundation", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "3929d9fe8148d17819ad0178c748b8d339420709"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/3929d9fe8148d17819ad0178c748b8d339420709", "reference": "3929d9fe8148d17819ad0178c748b8d339420709", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php54": "~1.0", "symfony/polyfill-php55": "~1.0"}, "require-dev": {"symfony/expression-language": "~2.4|~3.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2019-11-12T12:34:41+00:00"}, {"name": "symfony/http-kernel", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "c3be27b8627cd5ee8dfa8d1b923982f618ec521c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/c3be27b8627cd5ee8dfa8d1b923982f618ec521c", "reference": "c3be27b8627cd5ee8dfa8d1b923982f618ec521c", "shasum": ""}, "require": {"php": ">=5.3.9", "psr/log": "~1.0", "symfony/debug": "^2.6.2", "symfony/event-dispatcher": "^2.6.7|~3.0.0", "symfony/http-foundation": "~2.7.36|~2.8.29|~3.1.6", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php56": "~1.8"}, "conflict": {"symfony/config": "<2.7", "twig/twig": "<1.34|<2.4,>=2"}, "require-dev": {"symfony/browser-kit": "~2.3|~3.0.0", "symfony/class-loader": "~2.1|~3.0.0", "symfony/config": "~2.8", "symfony/console": "~2.3|~3.0.0", "symfony/css-selector": "^2.0.5|~3.0.0", "symfony/dependency-injection": "~2.8|~3.0.0", "symfony/dom-crawler": "^2.0.5|~3.0.0", "symfony/expression-language": "~2.4|~3.0.0", "symfony/finder": "^2.0.5|~3.0.0", "symfony/process": "^2.0.5|~3.0.0", "symfony/routing": "~2.8|~3.0.0", "symfony/stopwatch": "~2.3|~3.0.0", "symfony/templating": "~2.2|~3.0.0", "symfony/translation": "^2.0.5|~3.0.0", "symfony/var-dumper": "~2.6|~3.0.0"}, "suggest": {"symfony/browser-kit": "", "symfony/class-loader": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/finder": "", "symfony/var-dumper": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2019-11-13T08:36:16+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php54", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php54.git", "reference": "37285b1d5d13f37c8bee546d8d2ad0353460c4c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php54/zipball/37285b1d5d13f37c8bee546d8d2ad0353460c4c7", "reference": "37285b1d5d13f37c8bee546d8d2ad0353460c4c7", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.4+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php55", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php55.git", "reference": "c17452124a883900e1d73961f9075a638399c1a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php55/zipball/c17452124a883900e1d73961f9075a638399c1a0", "reference": "c17452124a883900e1d73961f9075a638399c1a0", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.5+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "5f03a781d984aae42cebd18e7912fa80f02ee644"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/5f03a781d984aae42cebd18e7912fa80f02ee644", "reference": "5f03a781d984aae42cebd18e7912fa80f02ee644", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "bf44a9fd41feaac72b074de600314a93e2ae78e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/bf44a9fd41feaac72b074de600314a93e2ae78e2", "reference": "bf44a9fd41feaac72b074de600314a93e2ae78e2", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/routing", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "8b0df6869d1997baafff6a1541826eac5a03d067"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/8b0df6869d1997baafff6a1541826eac5a03d067", "reference": "8b0df6869d1997baafff6a1541826eac5a03d067", "shasum": ""}, "require": {"php": ">=5.3.9"}, "conflict": {"symfony/config": "<2.7"}, "require-dev": {"doctrine/annotations": "~1.0", "psr/log": "~1.0", "symfony/config": "~2.7|~3.0.0", "symfony/expression-language": "~2.4|~3.0.0", "symfony/http-foundation": "~2.3|~3.0.0", "symfony/yaml": "^2.0.5|~3.0.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/dependency-injection": "For loading routes from a service", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2018-11-20T15:55:20+00:00"}, {"name": "symfony/security-core", "version": "v3.4.49", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "6eea784297bd604efc169e1fc6b63c55b25d5bc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/6eea784297bd604efc169e1fc6b63c55b25d5bc6", "reference": "6eea784297bd604efc169e1fc6b63c55b25d5bc6", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-php56": "~1.0"}, "require-dev": {"psr/container": "^1.0", "psr/log": "~1.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/ldap": "~3.1|~4.0", "symfony/validator": "^3.2.5|~4.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/event-dispatcher": "", "symfony/expression-language": "For using the expression voter", "symfony/http-foundation": "", "symfony/ldap": "For using LDAP integration", "symfony/validator": "For using the user password constraint"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-19T12:03:21+00:00"}, {"name": "symfony/security-csrf", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "16f687748a2f2a63b7538642615d54f4f001fcf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/16f687748a2f2a63b7538642615d54f4f001fcf2", "reference": "16f687748a2f2a63b7538642615d54f4f001fcf2", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-php56": "~1.0", "symfony/polyfill-php70": "~1.0", "symfony/security-core": "~2.8|~3.0|~4.0"}, "conflict": {"symfony/http-foundation": "<2.8.31|~3.3,<3.3.13"}, "require-dev": {"symfony/http-foundation": "^2.8.31|~3.3.13|~3.4|~4.0"}, "suggest": {"symfony/http-foundation": "For using the class SessionTokenStorage."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/stopwatch", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "49c0ea2f3d3a779df4780927671332edc406ea84"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/49c0ea2f3d3a779df4780927671332edc406ea84", "reference": "49c0ea2f3d3a779df4780927671332edc406ea84", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2016-06-29T05:40:00+00:00"}, {"name": "symfony/templating", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/templating.git", "reference": "8e5cc0417316d9fe62fb78e474f98e5f4d95dbfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/templating/zipball/8e5cc0417316d9fe62fb78e474f98e5f4d95dbfd", "reference": "8e5cc0417316d9fe62fb78e474f98e5f4d95dbfd", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"psr/log": "~1.0"}, "suggest": {"psr/log": "For using debug logging in loaders"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Templating\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Templating Component", "homepage": "https://symfony.com", "time": "2016-07-30T07:22:48+00:00"}, {"name": "symfony/translation", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "fc58c2a19e56c29f5ba2736ec40d0119a0de2089"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/fc58c2a19e56c29f5ba2736ec40d0119a0de2089", "reference": "fc58c2a19e56c29f5ba2736ec40d0119a0de2089", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.7"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8", "symfony/intl": "~2.7.25|^2.8.18|~3.2.5", "symfony/yaml": "~2.2|~3.0.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2018-11-24T21:16:41+00:00"}, {"name": "symfony/twig-bridge", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "34ddcc46f09f6564f03cb61134ee51f3b309aa58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/34ddcc46f09f6564f03cb61134ee51f3b309aa58", "reference": "34ddcc46f09f6564f03cb61134ee51f3b309aa58", "shasum": ""}, "require": {"php": ">=5.5.9", "twig/twig": "~1.23|~2.0"}, "require-dev": {"symfony/asset": "~2.8|~3.0", "symfony/console": "~2.8|~3.0", "symfony/expression-language": "~2.8|~3.0", "symfony/finder": "~2.8|~3.0", "symfony/form": "~3.0.4", "symfony/http-kernel": "~2.8|~3.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "~2.8|~3.0", "symfony/security": "~2.8|~3.0", "symfony/security-acl": "~2.8|~3.0", "symfony/stopwatch": "~2.8|~3.0", "symfony/templating": "~2.8|~3.0", "symfony/translation": "~2.8|~3.0", "symfony/var-dumper": "~2.8.9|~3.0.9|~3.1.3|~3.2", "symfony/yaml": "~2.8|~3.0"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security": "For using the SecurityExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/templating": "For using the TwigEngine", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Twig Bridge", "homepage": "https://symfony.com", "time": "2016-07-28T11:13:34+00:00"}, {"name": "symfony/twig-bundle", "version": "v2.8.52", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "e74af27dbe64dd4e5e7e3dfc9bcf480253b76c80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/e74af27dbe64dd4e5e7e3dfc9bcf480253b76c80", "reference": "e74af27dbe64dd4e5e7e3dfc9bcf480253b76c80", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/asset": "~2.7|~3.0.0", "symfony/http-foundation": "~2.5|~3.0.0", "symfony/http-kernel": "~2.7.23|^2.8.16", "symfony/polyfill-ctype": "~1.8", "symfony/twig-bridge": "~2.7|~3.0.0", "twig/twig": "~1.34|~2.4"}, "require-dev": {"doctrine/annotations": "~1.0", "symfony/config": "~2.8|~3.0.0", "symfony/dependency-injection": "^2.6.6|~3.0.0", "symfony/expression-language": "~2.4|~3.0.0", "symfony/finder": "^2.0.5", "symfony/framework-bundle": "~2.7|~3.0.0", "symfony/routing": "~2.1|~3.0.0", "symfony/stopwatch": "~2.2|~3.0.0", "symfony/templating": "~2.1|~3.0.0", "symfony/yaml": "~2.3|~3.0.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony TwigBundle", "homepage": "https://symfony.com", "time": "2018-11-11T11:18:13+00:00"}, {"name": "symfony/var-dumper", "version": "v4.0.15", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "3af63f44ddb45b03af4d172a4ce3e5c58b25fc5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/3af63f44ddb45b03af4d172a4ce3e5c58b25fc5b", "reference": "3af63f44ddb45b03af4d172a4ce3e5c58b25fc5b", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "require-dev": {"ext-iconv": "*", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2018-07-26T11:22:46+00:00"}, {"name": "twig/twig", "version": "v1.44.7", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "0887422319889e442458e48e2f3d9add1a172ad5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/0887422319889e442458e48e2f3d9add1a172ad5", "reference": "0887422319889e442458e48e2f3d9add1a172ad5", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.44-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2022-09-28T08:38:36+00:00"}, {"name": "upstatement/routes", "version": "0.3", "source": {"type": "git", "url": "https://github.com/Upstatement/routes.git", "reference": "40d003b69c0f5c52fb4b15e5d1fa4d5c522c9475"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Upstatement/routes/zipball/40d003b69c0f5c52fb4b15e5d1fa4d5c522c9475", "reference": "40d003b69c0f5c52fb4b15e5d1fa4d5c522c9475", "shasum": ""}, "require": {"altorouter/altorouter": "1.1.0", "composer/installers": "~1.0", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*", "satooshi/php-coveralls": "dev-master", "wp-cli/wp-cli": "*"}, "type": "library", "autoload": {"psr-0": {"Routes": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://upstatement.com"}], "description": "Manage rewrites and routes in WordPress with this dead-simple plugin", "homepage": "http://routes.upstatement.com", "keywords": ["redirects", "rewrite", "routes", "routing"], "time": "2015-03-07T13:41:29+00:00"}, {"name": "vlucas/phpdotenv", "version": "v2.6.9", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141", "reference": "2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141", "shasum": ""}, "require": {"php": "^5.3.9 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T22:59:22+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2022-06-03T18:03:27+00:00"}, {"name": "wp-cli/mustangostang-spyc", "version": "0.6.3", "source": {"type": "git", "url": "https://github.com/wp-cli/spyc.git", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/spyc/zipball/6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "4.3.*@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "autoload": {"files": ["includes/functions.php"], "psr-4": {"Mustangostang\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "mustangostang", "email": "<EMAIL>"}], "description": "A simple YAML loader/dumper class for PHP (WP-CLI fork)", "homepage": "https://github.com/mustangostang/spyc/", "time": "2017-04-25T11:26:20+00:00"}, {"name": "wp-cli/php-cli-tools", "version": "v0.11.15", "source": {"type": "git", "url": "https://github.com/wp-cli/php-cli-tools.git", "reference": "b6edd35988892ea1451392eb7a26d9dbe98c836d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/php-cli-tools/zipball/b6edd35988892ea1451392eb7a26d9dbe98c836d", "reference": "b6edd35988892ea1451392eb7a26d9dbe98c836d", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "type": "library", "autoload": {"files": ["lib/cli/cli.php"], "psr-0": {"cli": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Console utilities for PHP", "homepage": "http://github.com/wp-cli/php-cli-tools", "keywords": ["cli", "console"], "time": "2022-08-15T10:15:55+00:00"}, {"name": "wp-cli/wp-cli", "version": "v2.7.0", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-cli.git", "reference": "832b7635a39b4e468c634572baaab2710a55d88b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-cli/zipball/832b7635a39b4e468c634572baaab2710a55d88b", "reference": "832b7635a39b4e468c634572baaab2710a55d88b", "shasum": ""}, "require": {"ext-curl": "*", "mustache/mustache": "^2.14.1", "php": "^5.6 || ^7.0 || ^8.0", "rmccue/requests": "^1.8", "symfony/finder": ">2.7", "wp-cli/mustangostang-spyc": "^0.6.3", "wp-cli/php-cli-tools": "~0.11.2"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.2 || ^2", "wp-cli/extension-command": "^1.1 || ^2", "wp-cli/package-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^3.1.6"}, "suggest": {"ext-readline": "Include for a better --prompt implementation", "ext-zip": "Needed to support extraction of ZIP archives when doing downloads or updates"}, "bin": ["bin/wp", "bin/wp.bat"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-0": {"WP_CLI\\": "php/"}, "classmap": ["php/class-wp-cli.php", "php/class-wp-cli-command.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI framework", "homepage": "https://wp-cli.org", "keywords": ["cli", "wordpress"], "time": "2022-10-12T05:13:17+00:00"}, {"name": "wpackagist-plugin/easy-wp-smtp", "version": "1.5.1", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/easy-wp-smtp/", "reference": "trunk"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/easy-wp-smtp.zip?timestamp=**********"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/easy-wp-smtp/"}, {"name": "wpackagist-plugin/jetpack", "version": "11.4", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/jetpack/", "reference": "tags/11.4"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/jetpack.11.4.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/jetpack/"}, {"name": "wpackagist-plugin/wordpress-importer", "version": "0.8", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/wordpress-importer/", "reference": "tags/0.8"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/wordpress-importer.0.8.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/wordpress-importer/"}, {"name": "zendframework/zend-mime", "version": "2.1.6", "source": {"type": "git", "url": "https://github.com/zendframework/zend-mime.git", "reference": "066d6eecff586a7fb10e8907c032beaf1a9d6104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-mime/zipball/066d6eecff586a7fb10e8907c032beaf1a9d6104", "reference": "066d6eecff586a7fb10e8907c032beaf1a9d6104", "shasum": ""}, "require": {"php": ">=5.3.3", "zendframework/zend-stdlib": "self.version"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "dev-master", "zendframework/zend-mail": "self.version"}, "suggest": {"zendframework/zend-mail": "Zend\\Mail component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev", "dev-develop": "2.2-dev"}}, "autoload": {"psr-4": {"Zend\\Mime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-mime", "keywords": ["mime", "zf2"], "abandoned": "laminas/laminas-mime", "time": "2013-04-17T13:32:54+00:00"}, {"name": "zendframework/zend-stdlib", "version": "2.1.6", "source": {"type": "git", "url": "https://github.com/zendframework/zend-stdlib.git", "reference": "0027339961ad3d49f91ee092e23f7269c18cb470"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/0027339961ad3d49f91ee092e23f7269c18cb470", "reference": "0027339961ad3d49f91ee092e23f7269c18cb470", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "dev-master", "zendframework/zend-eventmanager": "self.version", "zendframework/zend-filter": "self.version", "zendframework/zend-serializer": "self.version", "zendframework/zend-servicemanager": "self.version"}, "suggest": {"pecl-weakref": "Implementation of weak references for Stdlib\\CallbackHandler", "zendframework/zend-eventmanager": "To support aggregate hydrator usage", "zendframework/zend-filter": "To support naming strategy hydrator usage", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-servicemanager": "To support hydrator plugin manager usage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev", "dev-develop": "2.2-dev"}}, "autoload": {"psr-4": {"Zend\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-stdlib", "keywords": ["stdlib", "zf2"], "abandoned": "laminas/laminas-stdlib", "time": "2013-04-17T13:32:54+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"cakephp/cakephp": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "1.1.0"}