# --------------------------------------------------------------- #
# Docker PHP project                                              #
# --------------------------------------------------------------- #
version: "3"

networks:
    internal:
        external: false

volumes:
    mysql-57-volume:
        labels:
            - volker.preserve=true # even though not using volker, it shouldn't be deleted by vnuke

services:
    beanstalk:
        build: .docker/beanstalk
        tty: true
        working_dir: /var/www
        networks:
            - internal
        volumes:
            - ~/.ssh:/root/.ssh
            - ./:/var/www

    app:
        build: .docker/php
        tty: true
        working_dir: /var/www/html
        environment:
            DEBUG: 1
        networks:
            - internal
        ports:
            - 8888:80
        volumes:
            - ~/.ssh:/root/.ssh
            - ~/.composer/auth.json:/root/.composer/auth.json
            - ./:/var/www/html/

    database:
        image: biarms/mysql:5.7
        networks:
            - internal
        ports:
            - 33081:3306
        volumes:
            - mysql-57-volume:/var/lib/mysql
        environment:
            MYSQL_DATABASE: "bon-voyage"
            MYSQL_ALLOW_EMPTY_PASSWORD: "true"
            MYSQL_ROOT_PASSWORD:
            MYSQL_ROOT_HOST: "%"

    node:
        build:
            dockerfile: .docker/node/Dockerfile
            context: .
        tty: true
        working_dir: /var/www/html
        networks:
            - internal
        volumes:
            - ~/.ssh:/root/.ssh
            - ./:/var/www/html/
        environment:
            PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: "true"
