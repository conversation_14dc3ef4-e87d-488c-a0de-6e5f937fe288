# CakePHP Navigation Integration with WordPress

This document provides a detailed explanation of how the navigation system is integrated between the CakePHP main site and the WordPress blog.

## Overview

The navigation system uses a client-server architecture where:

1. The CakePHP application serves as the server, providing API endpoints that return HTML markup for the navigation menus
2. The WordPress blog acts as the client, fetching the navigation markup via AJAX and injecting it into the page

This approach ensures that both sites share the same navigation structure and content, making maintenance easier and providing a consistent user experience.

## Assumptions

* The CakePHP application is accessible at the root domain (e.g., `https://bon-voyage.ddev.site/`)
* The WordPress blog is installed in a subdirectory (e.g., `https://bon-voyage.ddev.site/blog/`)
* The navigation data is managed in the CakePHP application's database

## CakePHP Implementation

### Navigation Component (`app/controllers/components/navigation.php`)

The Navigation component is responsible for retrieving and formatting the navigation data from the database:

```php
<?php
class NavigationComponent extends Object {
    var $name = 'Navigation';
    var $uses = array('Destination', 'HolidayType', 'Spotlight', 'Page');

    function initialize(&$controller, $settings = array()) {
        $this->controller = $controller;
        $this->initialized = true;
    }

    public function getNavigationData() {
        // Fetch all navigation data
        $usaDestinations = $this->getUsaDestinations();
        $canadaDestinations = $this->getCanadaDestinations();
        $holidayTypes = $this->getHolidayTypes();
        $whatsHot = $this->getWhatsHot();
        $holidayInfoPages = $this->getHolidayInfoPages();
        $aboutPages = $this->getAboutPages();

        // Return structured navigation data
        return array(
            'mainNav' => array(
                'usa' => array(
                    'text' => 'USA',
                    'url' => '/destinations/usa_holidays',
                    'items' => $usaDestinations
                ),
                'canada' => array(
                    'text' => 'Canada',
                    'url' => '/destinations/canada_holidays',
                    'items' => $canadaDestinations
                ),
                'holiday_types' => array(
                    'text' => 'Holiday Types',
                    'url' => '/holidays',
                    'items' => $holidayTypes
                )
            ),
            'usaDestinations' => $usaDestinations,
            'canadaDestinations' => $canadaDestinations,
            'holidayTypes' => $holidayTypes,
            'whatsHot' => $whatsHot,
            'holidayInfoPages' => $holidayInfoPages,
            'aboutPages' => $aboutPages
        );
    }

    // Additional methods for fetching specific navigation sections...
}
?>
```

### Navigation Controller (`app/controllers/navigation_controller.php`)

The Navigation controller provides API endpoints that return HTML markup for the navigation menus:

```php
<?php
class NavigationController extends AppController {
    var $name = 'Navigation';
    var $uses = array('Destination', 'HolidayType', 'Spotlight', 'Page');
    var $components = array('Navigation', 'RequestHandler');
    var $helpers = array('App');

    /**
     * Before filter - allow API endpoints without authentication
     */
    function beforeFilter() {
        parent::beforeFilter();

        // Allow API endpoints without authentication
        if ($this->Auth->allowedActions <> array('*')) {
            $this->Auth->allowedActions = array_merge(
                $this->Auth->allowedActions,
                array('megamenu', 'mmenu')
            );
        }

        // Set HTML response type for API endpoints
        if (in_array($this->action, array('megamenu', 'mmenu'))) {
            $this->RequestHandler->respondAs('html');
            $this->layout = 'ajax';
        }
    }

    /**
     * API endpoint to get the megamenu HTML
     * This can be called from the WordPress blog to get the latest megamenu content
     */
    function megamenu() {
        // Get navigation data
        $navigationData = $this->Navigation->getNavigationData();

        // Extract navigation components
        $mainNav = $navigationData['mainNav'];
        $usaDestinations = $navigationData['usaDestinations'];
        $canadaDestinations = $navigationData['canadaDestinations'];
        $holidayTypes = $navigationData['holidayTypes'];
        $whatsHot = $navigationData['whatsHot'];
        $holidayInfoPages = $navigationData['holidayInfoPages'];
        $aboutPages = $navigationData['aboutPages'];

        // Set variables for the view
        $this->set(compact('mainNav', 'usaDestinations', 'canadaDestinations', 'holidayTypes', 'whatsHot', 'holidayInfoPages', 'aboutPages'));

        // Render the megamenu element
        $this->render('/elements/chrome/mega_menu_api');
    }

    /**
     * API endpoint to get the mmenu HTML
     * This can be called from the WordPress blog to get the latest mobile menu content
     */
    function mmenu() {
        // Get navigation data
        $navigationData = $this->Navigation->getNavigationData();

        // Build mobile navigation structure
        $mobileNavigation = array();

        // Add navigation sections...

        // Set variables for the view
        $this->set(compact('mobileNavigation'));

        // Render the mmenu element
        $this->render('/elements/chrome/mmenu_api');
    }
}
?>
```

### Routes Configuration (`app/config/routes.php`)

The routes configuration defines the API endpoints:

```php
<?php
// API endpoints for navigation
Router::connect('/api/navigation/megamenu', array(
    'controller' => 'navigation',
    'action' => 'megamenu'
));

Router::connect('/api/navigation/mmenu', array(
    'controller' => 'navigation',
    'action' => 'mmenu'
));
?>
```

## WordPress Implementation

### Navigation Plugin (`app/webroot/blog/app/mu-plugins/bon-voyage-navigation.php`)

The WordPress plugin handles loading the navigation from the CakePHP API endpoints:

```php
<?php
/**
 * Plugin Name: Bon Voyage Navigation
 * Description: Integrates the navigation from the main site into the WordPress blog
 * Version: 1.0
 * Author: Bon Voyage
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class BonVoyage_Navigation {
    /**
     * Constructor
     */
    public function __construct() {
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add mobile menu HTML to the footer
        add_action('wp_footer', array($this, 'add_mobile_menu_html'));

        // Add script to move the megamenu to the right place
        add_action('wp_footer', array($this, 'add_megamenu_placement_script'));
    }

    /**
     * Enqueue necessary scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue mmenu JavaScript
        wp_enqueue_script(
            'mmenu-js',
            '/js/vendor/mmenu/mmenu.min.js',
            array('jquery'),
            '8.5.24',
            true
        );

        // Enqueue navigation JavaScript
        wp_enqueue_script(
            'navigation-js',
            plugin_dir_url(__FILE__) . 'js/navigation.js',
            array('jquery', 'mmenu-js'),
            filemtime(plugin_dir_path(__FILE__) . 'js/navigation.js'),
            true
        );
    }

    /**
     * Add mobile menu HTML to the page
     */
    public function add_mobile_menu_html() {
        // Try to fetch the mobile menu HTML from the main site
        $menu_html = $this->fetch_mobile_menu_from_main_site();

        // If we couldn't fetch the menu, use the fallback
        if (empty($menu_html)) {
            $menu_html = $this->get_fallback_mobile_menu_html();
        }

        // Output the menu HTML
        echo $menu_html;
    }

    /**
     * Fetch the mobile menu HTML from the main site
     *
     * @return string The mobile menu HTML or empty string if fetch failed
     */
    private function fetch_mobile_menu_from_main_site() {
        // URL to the mobile menu API endpoint on the main site
        $api_url = '/api/navigation/mmenu';

        // Try to fetch the menu HTML
        $response = wp_remote_get($api_url, array(
            'timeout' => 5, // Short timeout to avoid slowing down the page
        ));

        // Check if the request was successful
        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            error_log('Failed to fetch mobile menu from main site: ' . (is_wp_error($response) ? $response->get_error_message() : wp_remote_retrieve_response_code($response)));
            return '';
        }

        // Get the response body
        $menu_html = wp_remote_retrieve_body($response);

        // Check if the response is valid HTML
        if (empty($menu_html) || !strpos($menu_html, '<nav id="mobile-menu"')) {
            error_log('Invalid mobile menu HTML received from main site');
            return '';
        }

        return $menu_html;
    }
}

// Initialize the plugin
new BonVoyage_Navigation();
?>
```

### Navigation JavaScript (`app/webroot/blog/app/mu-plugins/js/navigation.js`)

The JavaScript file handles loading and initializing the navigation menus:

```javascript
/* global jQuery, window, document, console */
/* jshint esversion: 5, browser: true, jquery: true, strict: true */

/**
 * BV Navigation System
 * Handles mobile and desktop navigation functionality
 */

(function($, window, document) {
    'use strict';

    // Cache commonly used elements
    var $document = $(document);
    var $window = $(window);
    var $body = $('body');

    // Configuration
    var CONFIG = {
        debug: true,
        prefix: '[BV Menu]',
        selectors: {
            mobileMenu: '#mobile-menu',
            megaMenu: '.mega-menu',
            menuToggle: '.mmenu-trigger',
            submenuToggle: '.submenu-toggle',
            megaMenuPlaceholder: '#megamenu-placeholder'
        },
        endpoints: {
            mobileMenu: '/api/navigation/mmenu',
            megaMenu: '/api/navigation/megamenu'
        }
    };

    /**
     * Mobile Menu functionality
     * @namespace
     */
    var MobileMenu = {
        /**
         * Load mobile menu content via AJAX
         * @returns {jQuery.Deferred} jQuery promise
         */
        load: function() {
            var self = this;
            var $mobileMenu = $(CONFIG.selectors.mobileMenu);
            var deferred = $.Deferred();

            if (!$mobileMenu.length) {
                var error = new Error('Mobile menu element not found');
                return deferred.reject(error).promise();
            }

            return $.ajax({
                url: CONFIG.endpoints.mobileMenu,
                method: 'GET',
                dataType: 'html'
            })
                .done(function(response) {
                    $mobileMenu.html(response);
                    self.initializeMobileMenu($mobileMenu);
                    $document.trigger('mobileMenu:loaded');
                    deferred.resolveWith($mobileMenu, [response]);
                })
                .fail(function(xhr, status, error) {
                    deferred.rejectWith($mobileMenu, [xhr, status, error]);
                });
        }
    };

    /**
     * Mega Menu functionality
     * @namespace
     */
    var MegaMenu = {
        /**
         * Load mega menu content via AJAX
         * @returns {jQuery.Deferred} jQuery promise
         */
        load: function() {
            var self = this;
            var $megaMenu = $(CONFIG.selectors.megaMenuPlaceholder);
            var deferred = $.Deferred();

            if (!$megaMenu.length) {
                var error = new Error('Mega menu placeholder not found');
                return deferred.reject(error).promise();
            }

            return $.ajax({
                url: CONFIG.endpoints.megaMenu,
                method: 'GET',
                dataType: 'html'
            })
                .done(function(response) {
                    $megaMenu.html(response);
                    self.initialize();
                    $document.trigger('megaMenu:loaded');
                    deferred.resolveWith($megaMenu, [response]);
                })
                .fail(function(xhr, status, error) {
                    deferred.rejectWith($megaMenu, [xhr, status, error]);
                });
        }
    };

    // Initialize when document is ready
    $document.ready(function() {
        // Load mobile menu
        MobileMenu.load();

        // Load mega menu
        MegaMenu.load();
    });

})(jQuery, window, document);
```

## Key Implementation Details

### API Endpoints

The navigation system uses two main API endpoints:

1. **Mobile Menu (mmenu) Endpoint**: `/api/navigation/mmenu`
   - Returns HTML markup for the mobile menu
   - Includes all navigation sections with proper hierarchy
   - Optimized for mobile display

2. **Mega Menu Endpoint**: `/api/navigation/megamenu`
   - Returns HTML markup for the desktop mega menu
   - Includes dropdown panels for each navigation section
   - Optimized for desktop display

### Error Handling

Both the CakePHP and WordPress sides include error handling:

- The WordPress plugin checks for HTTP errors when fetching menu content
- Failed requests fall back to hardcoded menu content
- Errors are logged for debugging purposes

### Security Considerations

The implementation includes several security measures:

- The CakePHP controller explicitly allows public access to the API endpoints
- The WordPress plugin sanitizes the HTML received from the API
- Relative URLs are used to avoid cross-domain issues

### JavaScript Integration

The JavaScript integration follows these principles:

- Modular architecture with separate components for mobile and mega menus
- AJAX requests to load menu content
- Event-driven approach for menu interactions
- Responsive behavior for different screen sizes

## Usage Instructions

1. **CakePHP Setup:**
   - Ensure the Navigation component is properly configured
   - Verify that the API endpoints are accessible
   - Check that the menu templates are rendering correctly

2. **WordPress Setup:**
   - Ensure the navigation.js file is properly enqueued
   - Add the mobile menu placeholder in the footer
   - Add the mega menu placeholder in the header
   - Verify that the API requests are successful

3. **Testing:**
   - Test the mobile menu on various mobile devices
   - Test the mega menu on desktop browsers
   - Verify that the hamburger icon toggles correctly
   - Check that all links in both menus work as expected

## Troubleshooting

If you encounter issues with the navigation system:

1. Check the browser console for JavaScript errors
2. Verify that the API endpoints are returning valid HTML
3. Ensure that the menu placeholders exist in the WordPress templates
4. Check that the mmenu.js library is properly loaded
5. Verify that the CSS styles are applied correctly
