# Mobile Menu (mmenu) Implementation Documentation

This document provides an overview of the mobile menu (mmenu) and megamenu implementation in the Bon Voyage WordPress blog, including how it integrates with the main CakePHP site.

## Current Implementation Overview

The navigation system in the WordPress blog is implemented through the WordPress plugin located in the `app/webroot/blog/app/mu-plugins/` directory:

1. **bon-voyage-navigation.php** - Active plugin that handles both mobile menu and megamenu
2. **js/navigation.js** - JavaScript file that handles the loading and initialization of both menus

The main site has API endpoints in `app/controllers/navigation_controller.php` that serve the menu content to the WordPress blog.

### API Endpoints

The WordPress blog loads navigation markup from the following API endpoints:

1. **Mobile Menu (mmenu) Endpoint**: `/api/navigation/mmenu`
   - Defined in the main site's routes.php
   - Implemented in NavigationController::mmenu()
   - Returns HTML markup for the mobile menu

2. **Mega Menu Endpoint**: `/api/navigation/megamenu`
   - Defined in the main site's routes.php
   - Implemented in NavigationController::megamenu()
   - Returns HTML markup for the desktop mega menu

### API Endpoint Configuration

The API endpoints are configured as follows:

1. **URL Path Structure**: The WordPress blog is installed at `/blog` while the main CakePHP application handles the root path:
   - The API endpoints are defined in the main CakePHP application at `/api/navigation/mmenu` and `/api/navigation/megamenu`
   - When the WordPress blog makes requests to these endpoints using relative URLs, they work correctly since they're outside the `/blog` path

2. **CakePHP Controller Configuration**: The NavigationController in the main site has the correct routes defined:
   - The controller adds 'mmenu' and 'megamenu' to `$this->Auth->allowedActions` to ensure they're accessible without authentication
   - The RequestHandler component is configured to respond with HTML content

3. **URL Construction**:
   - The navigation.js file uses relative URLs: `/api/navigation/mmenu` and `/api/navigation/megamenu`
   - These relative URLs resolve correctly from the WordPress blog context

4. **Error Handling**: The system includes error handling for API endpoint failures:
   - Failed requests fall back to hardcoded menu content
   - Errors are logged to help diagnose issues

## Implementation Details

### 1. Navigation System Architecture

The navigation system follows a client-server architecture:

- **Server (CakePHP)**: Provides API endpoints that serve HTML markup for the menus
- **Client (WordPress)**: Fetches the menu markup via AJAX and injects it into the page

This approach ensures that both sites share the same navigation structure and content.

### 2. JavaScript Implementation

The navigation system uses a modular JavaScript architecture:

- **MobileMenu Module**: Handles loading and initializing the mobile menu
- **MegaMenu Module**: Handles loading and initializing the desktop mega menu
- **EventHandlers Module**: Manages event listeners for menu interactions
- **MenuUtils Module**: Provides utility functions for logging and state management

The main JavaScript file (`navigation.js`) is shared between the main site and the WordPress blog to ensure consistent behavior.

### 3. Mobile Menu (mmenu) Implementation

The mobile menu uses the mmenu.js library with the following configuration:

```javascript
var menu = new MmenuConstructor("#mobile-menu", {
    "offCanvas": {
        "position": "left",
        "onClick": {
            "close": false
        }
    },
    "scrollBugFix": { "fix": true },
    "theme": "light",
    "slidingSubmenus": true,
    "extensions": [
        "pagedim-black",
    ],
    "navbar": {
        "title": "Menu",
        "titleLink": "parent"
    },
    "navbars": [
        {
            "content": [
                '<img src="/img/site/sprites/logos/bv-logo-red.svg" alt="Bon Voyage" style="height: 50px;">',
            ],
            "position": "top"
        },
        {
            "position": "top",
            "content": [
                "prev",
                "title",
            ]
        }
    ]
});
```

The mobile menu is loaded from the `/api/navigation/mmenu` endpoint and initialized when the document is ready.

### 4. Mega Menu Implementation

The mega menu is implemented as a series of dropdown panels that appear when hovering over navigation items. The implementation includes:

- Dropdown triggers in the secondary navigation
- Dropdown panels with content for each section
- Event handlers for mouseenter/mouseleave events
- Responsive behavior that hides the mega menu on mobile devices

The mega menu is loaded from the `/api/navigation/megamenu` endpoint and initialized when the document is ready.

### 5. Responsive Behavior

The navigation system includes responsive behavior that switches between mobile and desktop views:

- On desktop (min-width: 1024px), the mega menu is shown and the mobile menu is hidden
- On mobile (max-width: 1023px), the mobile menu is accessible via the hamburger icon and the mega menu is hidden
- A media query listener handles viewport changes and ensures the appropriate menu is displayed

### 6. Hamburger/Cross Icon Toggle

The hamburger icon changes to a cross (X) when the mobile menu is open:

- The hamburger icon is shown by default
- When the menu is opened, the hamburger icon is hidden and the cross icon is shown
- When the menu is closed, the cross icon is hidden and the hamburger icon is shown

This toggle is handled by the mmenu API's open/close events.

## Recommendations for Future Improvements

1. **Implement Caching**: Cache API responses to improve performance and reduce dependency on the main site.

2. **Optimize Resource Loading**: Ensure resources are loaded only once and from consistent sources.

3. **Conditional Debug Logging**: Make console logging conditional based on environment.

4. **Improve Error Handling**: Implement more comprehensive error handling for API requests.

5. **Use WordPress Functions for URLs**: Replace hardcoded URLs with WordPress functions like `home_url()`.

6. **Implement Service Worker**: Consider using a service worker to cache navigation content for offline use.

7. **Add Analytics**: Add analytics tracking to menu interactions to understand user behavior.

8. **Accessibility Improvements**: Ensure the navigation system meets WCAG accessibility standards.

9. **Performance Monitoring**: Add performance monitoring to track loading times and optimize as needed.
