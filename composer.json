{"name": "mozmorris/bon-voyage", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"ContentHub\\": "vendors/ContentHub"}}, "repositories": [{"type": "composer", "url": "https://wpackagist.org"}, {"type": "vcs", "url": "https://github.com/tomb1n0/cakephp"}], "require": {"besimple/soap": "^0.2.6", "cakephp/cakephp": "dev-php7.2-migration", "composer/installers": "^1.0", "composer/semver": "1.4.0", "jarednova/timber": "^0.22.5", "jenssegers/proxy": "~2.2", "johnpbloch/wordpress": "^6.0", "oscarotero/env": "^1.0", "phan/phan": "^1.2", "psy/psysh": "~0.6", "vlucas/phpdotenv": "^2.2", "wp-cli/wp-cli": "^2.7.0", "wpackagist-plugin/easy-wp-smtp": "^1.5.1", "wpackagist-plugin/jetpack": "^11.4", "wpackagist-plugin/wordpress-importer": "^0.8"}, "extra": {"installer-paths": {"app/webroot/blog/app/mu-plugins/{$name}/": ["type:wordpress-muplugin", "jarednova/timber"], "app/webroot/blog/app/plugins/{$name}/": ["type:wordpress-plugin"], "app/webroot/blog/app/themes/{$name}/": ["type:wordpress-theme"]}, "wordpress-install-dir": "app/webroot/blog/wp"}}