{"name": "BonVoyage", "private": true, "version": "2.0.0", "author": "<PERSON><PERSON>", "scripts": {"serve": "gulp serve", "build": "gulp"}, "devDependencies": {"@babel/core": "^7.19.3", "@babel/preset-env": "^7.22.15", "@babel/register": "^7.18.9", "babel-core": "^6.26.3", "babel-loader": "^9.1.3", "babel-preset-es2015": "^6.5.0", "breakpoint-sass": "^2.7.1", "critical": "^0.7.3", "criticalcss": "^1.0.1", "del": "^2.2.0", "gulp-autoprefixer": "^3.1.0", "gulp-babel": "^6.1.2", "gulp-cache": "^0.4.2", "gulp-cssnano": "^2.1.1", "gulp-debug": "^4.0.0", "gulp-eslint": "^2.0.0", "gulp-htmlmin": "^1.3.0", "gulp-if": "^2.0.0", "gulp-imagemin": "^2.4.0", "gulp-load-plugins": "^1.2.0", "gulp-plumber": "^1.1.0", "gulp-replace": "^0.5.4", "gulp-ruby-sass": "^2.0.6", "gulp-size": "^2.0.0", "gulp-sourcemaps": "^1.6.0", "gulp-uglify": "^3.0.2", "gulp-useref": "^3.1.0", "handlebars": "^3.0.0", "lodash": "^3.1.0", "vue": "^3.3.4", "vue-loader": "^17.2.2", "vue-template-compiler": "^2.7.14", "webpack": "^5.88.2", "wiredep": "^3.0.0"}, "engines": {"node": ">=0.12.0"}, "eslintConfig": {"env": {"es6": true, "node": true, "browser": true}, "rules": {"quotes": [2, "single"]}}, "dependencies": {"babel-register": "^6.26.0", "bower": "^1.8.14", "gulp": "^4.0.2"}}