<?php
/**
 * Extract Form Data Script
 * 
 * This script extracts unique form submissions from the logs and displays them in a readable format.
 * It handles both form_submissions.log and crm_integration.log files.
 */

// Configuration
$logsDir = __DIR__ . '/app/tmp/logs/';
$formLogFile = $logsDir . 'form_submissions.log';
$crmLogFile = $logsDir . 'crm_integration.log';

// Check if files exist
if (!file_exists($formLogFile)) {
    die("Error: Form submissions log file not found at $formLogFile\n");
}

echo "Extracting unique form submissions...\n\n";

// Process form submissions log
$submissions = [];
$uniqueKeys = [];
$crmData = [];
$emailToSubmissions = []; // Track submissions by email
$validatedSubmissions = []; // Track validated submissions by request ID
$failedSubmissions = []; // Track failed submissions that don't have form data

// Function to parse JSON objects from a log file
function parseJsonFromLog($filePath) {
    $content = file_get_contents($filePath);
    // Split the content by JSON objects (each object starts with '{' and ends with '}')
    $jsonObjects = preg_split('/}\s*{/', $content);
    
    $results = [];
    foreach ($jsonObjects as $index => $jsonStr) {
        // Fix the JSON string by adding the missing braces
        if ($index > 0) {
            $jsonStr = '{' . $jsonStr;
        }
        if ($index < count($jsonObjects) - 1) {
            $jsonStr = $jsonStr . '}';
        }
        
        // Decode the JSON
        $data = json_decode($jsonStr, true);
        if ($data) {
            $results[] = $data;
        }
    }
    
    return $results;
}

// Parse form submissions log
$formLogEntries = parseJsonFromLog($formLogFile);

// First pass: identify validated and failed submissions
foreach ($formLogEntries as $data) {
    if (isset($data['message']) && isset($data['request_id'])) {
        $message = $data['message'];
        $requestId = $data['request_id'];
        
        // Track validation status
        if (strpos($message, 'Form validation passed') !== false) {
            $validatedSubmissions[$requestId] = 'passed';
        } elseif (strpos($message, 'Recaptcha validation failed') !== false || 
                  strpos($message, 'Validation errors') !== false ||
                  (strpos($message, 'Form submission started') !== false)) {
            $validatedSubmissions[$requestId] = 'failed';
            
            // Store failed submissions that don't have form data
            if (!isset($data['form_data'])) {
                $failedSubmissions[] = [
                    'timestamp' => $data['timestamp'],
                    'ip' => $data['ip'] ?? 'unknown',
                    'session_id' => $data['session_id'] ?? 'unknown',
                    'request_id' => $requestId,
                    'validated' => 'failed',
                    'data' => ['message' => $message], // Store the failure message
                    'unique_key' => md5($requestId) // Use request ID as unique key
                ];
            }
        }
    }
}

// Extract form submissions
foreach ($formLogEntries as $data) {
    // Check if this is a form submission with data
    if (isset($data['form_data']) && isset($data['form_data']['TravelPlan'])) {
        $travelPlan = $data['form_data']['TravelPlan'];
        
        // Only process entries that have an email address (actual form submissions)
        if (isset($travelPlan['email_address'])) {
            // Create a unique key based on email and form data
            $email = $travelPlan['email_address'];
            $timestamp = $data['timestamp'] ?? '';
            
            // Create a more robust unique key by combining email with other identifying data
            // This helps identify truly unique submissions vs log duplicates
            $uniqueData = [
                'email' => $email,
                'name' => ($travelPlan['first_name'] ?? '') . ' ' . ($travelPlan['last_name'] ?? ''),
                'phone' => $travelPlan['telephone_number'] ?? '',
                'destination' => ($travelPlan['destination_country'] ?? '') . ' ' . ($travelPlan['destination_region'] ?? ''),
                'travel_date' => $travelPlan['travel_date'] ?? ($travelPlan['travel_date_month'] ?? '') . ' ' . ($travelPlan['travel_date_year'] ?? '')
            ];
            
            // Create a hash of the unique data to use as a key
            $key = md5(json_encode($uniqueData));
            
            // Check if this is a "form submission started" log entry or a complete submission
            $isCompleteSubmission = isset($travelPlan['travel_date']) || isset($travelPlan['source']);
            
            // Mark if this submission has been validated
            $isValidated = isset($data['request_id']) && isset($validatedSubmissions[$data['request_id']]);
            
            // Only add if we haven't seen this submission before or if this is a more complete version
            if (!isset($uniqueKeys[$key]) || $isCompleteSubmission) {
                // If we've seen this key before but this is a more complete version, replace it
                if (isset($uniqueKeys[$key])) {
                    // Find and remove the previous version
                    foreach ($submissions as $index => $existingSubmission) {
                        if (isset($existingSubmission['unique_key']) && $existingSubmission['unique_key'] === $key) {
                            // If the existing submission is validated but the new one isn't, keep the validated one
                            if ($existingSubmission['validated'] && !$isValidated) {
                                continue 2; // Skip to the next iteration of the outer loop
                            }
                            unset($submissions[$index]);
                            break;
                        }
                    }
                }
                
                $uniqueKeys[$key] = true;
                $submission = [
                    'timestamp' => $data['timestamp'],
                    'data' => $travelPlan,
                    'ip' => $data['ip'] ?? 'unknown',
                    'session_id' => $data['session_id'] ?? 'unknown',
                    'request_id' => $data['request_id'] ?? 'unknown',
                    'unique_key' => $key, // Store the key for later reference
                    'validated' => isset($data['request_id']) && isset($validatedSubmissions[$data['request_id']]) ? 
                        $validatedSubmissions[$data['request_id']] : 'unknown' // Store validation status
                ];
                
                $submissions[] = $submission;
                
                // Track submissions by email for the --latest option
                if (!isset($emailToSubmissions[$email])) {
                    $emailToSubmissions[$email] = [];
                }
                $emailToSubmissions[$email][] = count($submissions) - 1; // Store the index
            }
        }
    }
}

// Merge failed submissions with regular submissions
$submissions = array_merge($submissions, $failedSubmissions);

// Parse CRM integration log if it exists
if (file_exists($crmLogFile)) {
    $crmLogEntries = parseJsonFromLog($crmLogFile);
    
    // Extract CRM data
    foreach ($crmLogEntries as $data) {
        // Look for prepared data for CRM
        if (isset($data['message']) && strpos($data['message'], 'Prepared data for CRM:') === 0) {
            // Extract the JSON part from the message
            $jsonStart = strpos($data['message'], '{');
            if ($jsonStart !== false) {
                $jsonStr = substr($data['message'], $jsonStart);
                $crmData[$data['request_id']] = [
                    'timestamp' => $data['timestamp'],
                    'data' => json_decode($jsonStr, true),
                    'session_id' => $data['session_id'] ?? 'unknown'
                ];
            }
        }
        
        // Look for CRM response URLs
        if (isset($data['message']) && strpos($data['message'], 'Returning URL:') === 0) {
            $url = trim(substr($data['message'], 14)); // Extract URL after "Returning URL: "
            if (isset($crmData[$data['request_id']])) {
                $crmData[$data['request_id']]['crm_url'] = $url;
            }
        }
    }
    
    // Match CRM data with form submissions
    foreach ($submissions as &$submission) {
        if (isset($submission['request_id']) && isset($crmData[$submission['request_id']])) {
            $submission['crm_data'] = $crmData[$submission['request_id']]['data'];
            $submission['crm_url'] = $crmData[$submission['request_id']]['crm_url'] ?? null;
        }
    }
}

// Sort submissions by timestamp (newest first)
usort($submissions, function($a, $b) {
    return strtotime($b['timestamp']) - strtotime($a['timestamp']);
});

// Process command line arguments
$latestOnly = false;
$todayOnly = false;
$startDate = null;
$endDate = null;
$emailFilter = null;
$exportToCsv = false;
$deduplicate = false;
$validatedOnly = false;
$unknownOnly = false;
$csvFilename = __DIR__ . '/form_submissions_export.csv';
$showHelp = false;

// Function to expand tilde to home directory
function expandTilde($path) {
    if (substr($path, 0, 1) !== '~') {
        return $path;
    }
    
    // Get the user's home directory
    if (isset($_SERVER['HOME'])) {
        $home = $_SERVER['HOME'];
    } else {
        $home = getenv('HOME');
    }
    
    // Replace the tilde with the home directory
    return $home . substr($path, 1);
}

// Check if no arguments were provided
if ($argc <= 1) {
    displayHelp();
    exit(0);
}

// Parse command line arguments
for ($i = 1; $i < $argc; $i++) {
    $arg = $argv[$i];
    
    if ($arg === '--latest') {
        $latestOnly = true;
    } elseif ($arg === '--today') {
        $todayOnly = true;
    } elseif ($arg === '--csv') {
        $exportToCsv = true;
    } elseif ($arg === '--deduplicate') {
        $deduplicate = true;
    } elseif ($arg === '--validated-only') {
        $validatedOnly = true;
    } elseif ($arg === '--unknown-only') {
        $unknownOnly = true;
    } elseif (strpos($arg, '--email=') === 0) {
        $emailFilter = substr($arg, 8); // Extract email after "--email="
    } elseif (strpos($arg, '--from=') === 0) {
        $startDate = substr($arg, 7); // Extract date after "--from="
    } elseif (strpos($arg, '--to=') === 0) {
        $endDate = substr($arg, 5); // Extract date after "--to="
    } elseif (strpos($arg, '--output=') === 0) {
        $csvFilename = substr($arg, 9); // Extract filename after "--output="
        // Expand tilde if present
        $csvFilename = expandTilde($csvFilename);
        if (!preg_match('/\.csv$/', $csvFilename)) {
            $csvFilename .= '.csv';
        }
        
        // Create directory if it doesn't exist
        $directory = dirname($csvFilename);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    } elseif ($arg === '--help' || $arg === '-h') {
        displayHelp();
        exit(0);
    } else {
        echo "Unknown option: $arg\n";
        displayHelp();
        exit(1);
    }
}

// Filter submissions based on command line arguments
$filteredSubmissions = $submissions;

// Filter by date range if specified
if ($startDate !== null || $endDate !== null || $todayOnly) {
    $filteredSubmissions = [];
    
    if ($todayOnly) {
        $today = date('Y-m-d');
        foreach ($submissions as $submission) {
            $submissionDate = substr($submission['timestamp'], 0, 10);
            if ($submissionDate === $today) {
                $filteredSubmissions[] = $submission;
            }
        }
        echo "Showing only submissions from today (" . $today . ").\n\n";
    } else {
        $startTimestamp = $startDate !== null ? strtotime($startDate) : 0;
        $endTimestamp = $endDate !== null ? strtotime($endDate . ' 23:59:59') : PHP_INT_MAX;
        
        foreach ($submissions as $submission) {
            $submissionTimestamp = strtotime($submission['timestamp']);
            if ($submissionTimestamp >= $startTimestamp && $submissionTimestamp <= $endTimestamp) {
                $filteredSubmissions[] = $submission;
            }
        }
        
        echo "Filtering submissions ";
        if ($startDate !== null) {
            echo "from " . date('Y-m-d', $startTimestamp) . " ";
        }
        if ($endDate !== null) {
            echo "to " . date('Y-m-d', $endTimestamp) . " ";
        }
        echo "\n\n";
    }
    
    $submissions = $filteredSubmissions;
}

// Filter by email if specified
if ($emailFilter !== null) {
    $filteredSubmissions = [];
    foreach ($submissions as $submission) {
        if (isset($submission['data']['email_address']) && $submission['data']['email_address'] === $emailFilter) {
            $filteredSubmissions[] = $submission;
        }
    }
    $submissions = $filteredSubmissions;
    echo "Showing only submissions for email: $emailFilter\n\n";
}

// Filter to show only validated submissions if specified
if ($validatedOnly) {
    echo "Showing only validated submissions that passed form validation in export.\n\n";
} elseif ($unknownOnly) {
    echo "Showing only submissions with unknown validation status in export.\n\n";
}

// Filter to show only the latest submission for each email
if ($latestOnly) {
    $latestSubmissions = [];
    $processedEmails = [];
    
    foreach ($submissions as $submission) {
        $email = $submission['data']['email_address'];
        if (!isset($processedEmails[$email])) {
            $latestSubmissions[] = $submission;
            $processedEmails[$email] = true;
        }
    }
    
    $submissions = $latestSubmissions;
    echo "Showing only the most recent submission for each email address.\n\n";
}

// Reindex the submissions array to ensure no gaps in array keys
$submissions = array_values($submissions);

// Deduplicate submissions if requested
if ($deduplicate) {
    $uniqueSubmissions = [];
    $processedKeys = [];
    
    foreach ($submissions as $submission) {
        // Create a deduplication key based on email and core form data
        $email = $submission['data']['email_address'] ?? '';
        $name = ($submission['data']['first_name'] ?? '') . ' ' . ($submission['data']['last_name'] ?? '');
        $phone = $submission['data']['telephone_number'] ?? '';
        
        $dedupeKey = md5($email . '|' . $name . '|' . $phone);
        
        if (!isset($processedKeys[$dedupeKey])) {
            $uniqueSubmissions[] = $submission;
            $processedKeys[$dedupeKey] = true;
        }
    }
    
    $submissions = $uniqueSubmissions;
    echo "Removed duplicate submissions based on email, name, and phone number.\n\n";
}

// Calculate statistics
$totalEntries = count($submissions);
$uniqueEmails = count(array_unique(array_map(function($s) { 
    return $s['data']['email_address'] ?? ''; 
}, $submissions)));

// Count validation statuses
$validationStats = array_reduce($submissions, function($carry, $s) {
    $status = $s['validated'];
    if (!isset($carry[$status])) {
        $carry[$status] = 0;
    }
    $carry[$status]++;
    return $carry;
}, []);

$validatedCount = $validationStats['passed'] ?? 0;
$failedValidationCount = $validationStats['failed'] ?? 0;
$unknownValidationCount = $validationStats['unknown'] ?? 0;

// Display statistics
echo "\n=== STATISTICS ===\n";
echo "Total entries processed: $totalEntries\n";
echo "Unique email addresses: $uniqueEmails\n";
echo "Entries that passed validation: $validatedCount\n";
echo "Entries that failed validation: $failedValidationCount\n";
echo "Entries with unknown validation status: $unknownValidationCount\n\n";

// Log statistics to CSV
$statsLogFile = expandTilde('~/form_stats.csv');

// Create header if file doesn't exist
if (!file_exists($statsLogFile)) {
    $fp = fopen($statsLogFile, 'w');
    fputcsv($fp, [
        'timestamp',
        'command_params',
        'total_entries',
        'unique_emails',
        'passed_validation',
        'failed_validation',
        'unknown_validation'
    ]);
    fclose($fp);
}

// Append statistics
$fp = fopen($statsLogFile, 'a');
fputcsv($fp, [
    date('Y-m-d H:i:s'),
    implode(' ', array_slice($argv, 1)),
    $totalEntries,
    $uniqueEmails,
    $validatedCount,
    $failedValidationCount,
    $unknownValidationCount
]);
fclose($fp);

// Display the results
echo "Found " . count($submissions) . " form submissions.\n\n";

// Export to CSV if requested
if ($exportToCsv) {
    // Filter submissions based on validation status
    $exportSubmissions = $submissions;
    if ($validatedOnly) {
        $exportSubmissions = array_filter($submissions, function($s) { return $s['validated'] === 'passed'; });
    } elseif ($unknownOnly) {
        $exportSubmissions = array_filter($submissions, function($s) { return $s['validated'] === 'unknown'; });
    }
    
    $csvData = [];
    
    // Prepare CSV header
    $csvHeader = [
        'timestamp', 'validation_status', 'ip', 'session_id', 'request_id',
    ];
    
    // Add form fields to header
    if (!empty($exportSubmissions)) {
        foreach (reset($exportSubmissions)['data'] as $key => $value) {
            $csvHeader[] = 'form_' . $key;
        }
    }
    
    // Add CRM URL to header
    $csvHeader[] = 'crm_url';
    
    $csvData[] = $csvHeader;
    
    // Add data rows
    foreach ($exportSubmissions as $submission) {
        $row = [
            $submission['timestamp'],
            ucfirst($submission['validated']), // Add validation status, capitalized
            $submission['ip'],
            $submission['session_id'],
            $submission['request_id'],
        ];
        
        // Add form data
        foreach ($csvHeader as $index => $header) {
            if ($index < 5) continue; // Skip the first 5 columns (timestamp, validation_status, ip, session_id, request_id)
            if ($header === 'crm_url') continue; // Skip CRM URL for now
            
            $fieldName = substr($header, 5); // Remove 'form_' prefix
            $row[] = $submission['data'][$fieldName] ?? '';
        }
        
        // Add CRM URL
        $row[] = $submission['crm_url'] ?? '';
        
        $csvData[] = $row;
    }
    
    // Write to CSV file
    $fp = fopen($csvFilename, 'w');
    foreach ($csvData as $row) {
        fputcsv($fp, $row, ',', '"', '\\');
    }
    fclose($fp);
    
    echo "Exported " . count($exportSubmissions) . " submissions to " . basename($csvFilename) . "\n\n";
} else if (!empty($submissions)) {
    // Display submissions in console
    echo "=== SUBMISSIONS ===\n\n";
    
    foreach ($submissions as $submission) {
        echo "Submission Time: " . $submission['timestamp'] . "\n";
        echo "IP Address: " . $submission['ip'] . "\n";
        echo "Session ID: " . $submission['session_id'] . "\n";
        echo "Request ID: " . $submission['request_id'] . "\n";
        
        // Update validation status display
        $validationStatus = 'Unknown';
        if ($submission['validated'] === 'passed') {
            $validationStatus = 'Passed';
        } elseif ($submission['validated'] === 'failed') {
            $validationStatus = 'Failed';
        }
        echo "Validation Status: $validationStatus\n";
        
        echo "Form Data:\n";
        foreach ($submission['data'] as $key => $value) {
            echo "  - $key: $value\n";
        }
        
        if (isset($submission['crm_url'])) {
            echo "CRM URL: " . $submission['crm_url'] . "\n";
        }
        
        echo "\n--------------------------------------------------\n\n";
    }
}

// Display help information
function displayHelp() {
    echo "Usage: php extract_form_data.php [options]\n\n";
    echo "Available options:\n";
    echo "  -h, --help           Display this help information\n";
    echo "  --today              Show only submissions from today\n";
    echo "  --from=YYYY-MM-DD    Show submissions from this date onwards\n";
    echo "  --to=YYYY-MM-DD      Show submissions up to this date\n";
    echo "  --email=EMAIL        Filter submissions by email address\n";
    echo "  --latest             Show only the most recent submission for each email\n";
    echo "  --csv                Export submissions to CSV\n";
    echo "  --output=FILENAME    Specify output filename for CSV export\n";
    echo "  --deduplicate        Remove duplicate submissions based on email, name, and phone number\n";
    echo "  --validated-only     Show only submissions that passed form validation\n";
    echo "  --unknown-only       Show only submissions with unknown validation status\n";
    echo "\n";
    echo "Examples:\n";
    echo "  php extract_form_data.php --today --csv\n";
    echo "  php extract_form_data.php --from=2023-01-01 --to=2023-01-31 --csv --output=january_submissions\n";
    echo "  php extract_form_data.php --email=<EMAIL>\n";
    echo "  php extract_form_data.php --latest --csv\n";
    echo "  php extract_form_data.php --validated-only --deduplicate\n";
    echo "  php extract_form_data.php --unknown-only --from=2023-01-01 --to=2023-01-31 --csv\n";
} 