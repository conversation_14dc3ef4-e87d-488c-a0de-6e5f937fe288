<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 330 330" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(1,0,0,1,-4146.3,-1698.68)">
        <g id="Price-R" serif:id="Price R" transform="matrix(1,0,0,1,-3228.28,1698.68)">
            <rect x="7374.58" y="0" width="329.371" height="329.371" style="fill:none;"/>
            <clipPath id="_clip1">
                <rect x="7374.58" y="0" width="329.371" height="329.371"/>
            </clipPath>
            <g clip-path="url(#_clip1)">
                <g transform="matrix(0.967682,0,0,0.967682,-1031.03,-551.056)">
                    <g transform="matrix(1.03583,0,0,1.03583,853.258,430.065)">
                        <circle cx="7726.42" cy="298.593" r="159.156" style="fill:white;"/>
                    </g>
                    <g transform="matrix(1.20299,0,0,1.20299,7962.34,260.526)">
                        <path d="M743.293,256.804C821.371,256.804 884.762,320.194 884.762,398.273C884.762,476.352 821.371,539.742 743.293,539.742C665.214,539.742 601.824,476.352 601.824,398.273C601.824,320.194 665.214,256.804 743.293,256.804ZM743.293,265.948C670.261,265.948 610.968,325.241 610.968,398.273C610.968,471.305 670.261,530.598 743.293,530.598C816.325,530.598 875.618,471.305 875.618,398.273C875.618,325.241 816.325,265.948 743.293,265.948Z" style="fill:rgb(168,0,0);"/>
                    </g>
                    <g transform="matrix(0.853586,0,0,1.41998,7274.21,-279.92)">
                        <path d="M1912.56,692.166C1929.54,693.265 1942.77,701.915 1942.77,712.408L1942.77,742.183C1942.77,765.159 1911.74,783.812 1873.52,783.812L1833.92,783.812C1795.7,783.812 1764.67,765.159 1764.67,742.183L1764.67,712.408C1764.67,701.667 1778.53,692.857 1796.08,692.101L1796.08,675.706C1796.08,656.384 1822.18,640.696 1854.32,640.696C1886.47,640.696 1912.56,656.384 1912.56,675.706L1912.56,692.166ZM1808.97,692.048L1819.55,692.048L1819.55,675.873C1819.55,664.335 1835.13,654.967 1854.32,654.967C1873.52,654.967 1889.1,664.335 1889.1,675.873L1889.1,692.048L1899.68,692.048L1899.68,675.706C1899.68,660.659 1879.35,648.443 1854.32,648.443C1829.29,648.443 1808.97,660.659 1808.97,675.706L1808.97,692.048ZM1830.67,692.048L1877.97,692.048L1877.97,675.873C1877.97,668.027 1867.37,661.657 1854.32,661.657C1841.27,661.657 1830.67,668.027 1830.67,675.873L1830.67,692.048ZM1931.64,742.183L1931.64,712.408C1931.64,704.863 1921.45,698.738 1908.9,698.738L1798.54,698.738C1785.99,698.738 1775.8,704.863 1775.8,712.408L1775.8,742.183C1775.8,761.466 1801.84,777.121 1833.92,777.121L1873.52,777.121C1905.6,777.121 1931.64,761.466 1931.64,742.183ZM1852.6,704.587C1882.76,704.587 1907.24,719.304 1907.24,737.431C1907.24,755.559 1882.76,770.276 1852.6,770.276C1822.45,770.276 1797.96,755.559 1797.96,737.431C1797.96,719.304 1822.45,704.587 1852.6,704.587ZM1852.6,709.869C1827.3,709.869 1806.75,722.219 1806.75,737.431C1806.75,752.644 1827.3,764.994 1852.6,764.994C1877.91,764.994 1898.45,752.644 1898.45,737.431C1898.45,722.219 1877.91,709.869 1852.6,709.869ZM1848.85,758.202L1848.85,753.637C1838.69,752.628 1832.38,748.255 1832.38,741.335L1840.77,741.335C1840.77,746.621 1845.57,749.552 1853.64,749.552C1860.44,749.552 1864.99,747.582 1864.99,744.17C1864.99,736.049 1833.26,740.758 1833.26,728.648C1833.26,723.795 1839.25,720.142 1848.85,719.326L1848.85,715.001L1856.92,715.001L1856.92,719.374C1866.27,720.239 1872.99,723.987 1873.23,729.657L1864.75,729.657C1864.59,725.861 1859.32,723.506 1852.76,723.506C1846.45,723.506 1842.13,725.429 1842.13,728.504C1842.13,736.097 1873.87,731.147 1873.87,744.122C1873.87,749.552 1866.75,753.108 1856.92,753.781L1856.92,758.202L1848.85,758.202Z" style="fill:rgb(168,0,0);"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
