$em-base: 16px;
$rem-base: 16px;
$breakpoints: (
    ( name: 'xs', breakpoint: null ),
    ( name: 'sm', breakpoint: 640px ),
    ( name: 'md', breakpoint: 768px, alias: 'tablet' ),
    ( name: 'lg', breakpoint: 1024px ),
    ( name: 'xl', breakpoint: 1280px, alias: 'desktop' ),
    ( name: '2xl', breakpoint: 1440px ),
);

@include register-breakpoint-aliases($breakpoints);

@import './spacing';
@import './font-sizes';
@import './colours';

:root {
    /*
    |--------------------------------------------------------------------------
    | Layout
    |--------------------------------------------------------------------------
    */
    // Layout and grid
    --layout-gutter: var(--spacing-20px);
    --grid-gutter: var(--spacing-12px);

    // --outer-max-width: #{rem-calc(1380px)};
    @include respond-to(tablet) {
        --layout-gutter: var(--spacing-32px);
        --grid-gutter: var(--spacing-20px);
    }
    @include respond-to(desktop) {
        --layout-gutter: var(--spacing-106px);
        --grid-gutter: var(--spacing-32px);
    }

    --layout-max-width: 100%;
    @include respond-to(sm) {
        --layout-max-width: 640px;
    }
    @include respond-to(md) {
        --layout-max-width: 768px;
    }
    @include respond-to(lg) {
        --layout-max-width: 1024px;
    }
    @include respond-to(xl) {
        --layout-max-width: 1280px;
    }
    @include respond-to('2xl') {
        --layout-max-width: 1440px;
    }

    // Border radius
    --rounded-xs: 2px;
    --rounded-sm: 4px;
    --rounded: 6px;
    --rounded-lg: 8px;
    --rounded-xl: 12px;

    // Shadows
    --shadow-xs: #{0 2px 4px rgba(0, 0, 0, 0.04)};
    --shadow-s: #{0 2px 6px rgba(46, 41, 78, 0.06)};
    --shadow: #{0 2px 10px rgba(46, 41, 78, 0.06)};
    --shadow-lg: #{0 2px 14px rgba(0, 0, 0, 0.07)};

    // Transition timing functions
    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

    // Transition speeds
    --speed-slower: 500ms;
    --speed-slow: 400ms;
    --speed-normal: 250ms;
    --speed-fast: 150ms;
    --speed-faster: 50ms;

    // Fonts
    --font-sans-light: 300;
    --font-sans-regular: 400;
    --font-sans-medium: 500;
    --font-sans-semi-bold: 600;
    --font-sans-bold: 700;

    // Headline 1
    --headline-1-text: var(--font-size-30px);
    --headline-1-colour: var(--colour-heading);
    --headline-1-line-height: 1.26;
    --headline-1-letter-spacing: 0;
    --headline-1-weight: var(--font-sans-bold);
    --headline-1-space-between-typography: var(--spacing-20px);

    // Inputs
    --input-padding: var(--spacing-10px) var(--spacing-10px) var(--spacing-12px) var(--spacing-10px);
    --input-rounded: var(--rounded-xs);
    --input-border-colour: var(--colour-grey-500);
    --input-background-colour: var(--colour-white);
    --input-shadow: var(--shadow-xs);
    --label-spacing: var(--spacing-16px);
    --input-spacing: var(--spacing-36px);
    --input-row-spacing: var(--spacing-16px);
    --input-placeholder-colour: var(--colour-grey-600);

    // Buttons
    --button-gap: var(--spacing-6px);
}
