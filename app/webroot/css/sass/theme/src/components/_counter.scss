.counter {
    display: flex;
    align-items: center;
    gap: var(--spacing-12px);
}

.counter__value {
    font-size: var(--font-size-26px);
    font-weight: var(--font-sans-bold);
    font-variant-numeric: tabular-nums;
}

.counter__button {
    background: var(--colour-red-800);
    color: var(--colour-white);
    border-radius: var(--rounded-xs);
    width: var(--spacing-32px);
    height: var(--spacing-32px);
    border: 0;
    line-height: 0;
    padding: 0;

    svg {
        width: var(--spacing-20px);
        height: var(--spacing-20px);
    }

    &:hover,
    &:focus {
        background: var(--colour-red-900);
    }

    &:disabled {
        background-color: var(--colour-grey-400);
        color: var(--colour-grey-600);
    }

}
