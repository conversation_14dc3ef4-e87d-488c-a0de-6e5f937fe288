.button {
    position: relative;
    z-index: 1;

    display: inline-block;
    width: fit-content;
    padding: var(--button-padding);
    overflow: hidden;

    color: var(--button-text-colour);
    font-weight: var(--button-weight);
    font-size: var(--button-text);
    letter-spacing: var(--button-letter-spacing);
    text-align: center;

    background: var(--button-surface);
    border: 1px solid var(--button-border);
    border-radius: var(--button-rounded);
    box-shadow: var(--button-shadow);
    cursor: pointer;

    appearance: none;
    user-select: none;
}

.button {
    --button-icon-colour: var(--colour-red-800);
    --button-text-colour: var(--colour-red-800);
    --button-weight: var(--font-sans-bold);
    --button-text: var(--font-size-14px);
    --button-padding: 0;
    --button-letter-spacing: normal;
    --button-surface: transparent;
    --button-border: transparent;
    --button-rounded: 0;
    --button-shadow: none;

    text-transform: uppercase;
}

.button--block {
    --button-icon-colour: var(--colour-white);
    --button-text-colour: var(--colour-white);
    --button-padding: var(--spacing-12px) var(--spacing-20px) var(--spacing-14px) var(--spacing-20px);
    --button-icon-padding: var(--spacing-18px);
    --button-surface: var(--colour-red-800);
    --button-border: var(--colour-red-800);
}

.button--with-arrow {
    display: inline-flex;
    gap: var(--button-gap);
    align-items: center;
    justify-content: center;

    &:not(.button--icon-left) {
        padding-right: var(--button-icon-padding, var(--button-padding));
    }

    &::after {
        display: inline-block;
        flex-shrink: 0;
        width: var(--button-icon-width, 10px);
        height: var(--button-icon-height, 10px);

        background: var(--button-icon-colour);

        content: '';

        mask-image: var(--button-icon-url);
        mask-repeat: no-repeat;
        mask-position: right center;
        mask-size: contain;
    }
}

.button--with-arrow {
    --button-icon-width: 14px;
    --button-icon-height: 14px;
    --button-icon-url: url('../../../../img/site/icons/chevron_right.svg');
}
