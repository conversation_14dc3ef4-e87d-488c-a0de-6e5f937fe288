@mixin range-thumb {
    appearance: none;
    pointer-events: all;
    width: 24px;
    height: 24px;
    background-color: #A80000;
    border-radius: 999px;
    cursor: pointer;
    border: 0;
}

.range {
    position: relative;
}

.range__values {
    font-weight: var(--font-sans-semi-bold);
    margin-bottom: var(--spacing-24px);
}

.range__input {
    position: absolute;

    appearance: none;
    height: 6px;
    width: 100%;
    max-width: 280px;
    background-color: #E7E6E4;
    pointer-events: none;

    /** This is duplicated else the styles do not apply across all browsers **/
    &::-webkit-slider-thumb {
        @include range-thumb();
    }

    &::-moz-range-thumb {
        @include range-thumb();
    }
}

.range__input--to {
    background-color: transparent;
}
