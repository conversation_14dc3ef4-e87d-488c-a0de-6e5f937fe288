.form {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    gap: var(--input-spacing) var(--spacing-12px);
    grid-auto-flow: row dense;

    > * {
        grid-column: 1 / -1;
    }
}

.form__row {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    gap: var(--input-row-spacing) var(--spacing-12px);
    grid-auto-flow: row dense;

    > * {
        grid-column: 1 / -1;
    }
}

.label {
    font-weight: var(--font-sans-bold);
    font-size: var(--font-size-18px);
    display: block;
    margin-bottom: var(--label-spacing);
}

.error {
    --input-border-colour: #F55151;
    --input-background-colour: #FFF5F5;
}

input[type="text"],
input[type="date"],
select,
textarea {
    background-color: var(--input-background-colour);
    border: 1px solid var(--input-border-colour);
    border-radius: var(--input-rounded);
    box-shadow: var(--input-shadow);
    padding: var(--input-padding);

    width: 100%;

    &::placeholder {
        color: var(--input-placeholder-colour);
    }
}

textarea {
    height: var(--spacing-96px);
}

select {
    appearance: none;
    padding-right: var(--spacing-52px);

    background-image: url('../../../../img/site/icons/chevron_down.svg');
    background-size: 18px;
    background-position: calc(100% - var(--spacing-16px)) center;
    background-repeat: no-repeat;

    &:disabled {
        background-color: var(--colour-grey-200);
        color: var(--colour-grey-600);
        border-color: var(--colour-grey-200);
        box-shadow: none;
        background-image: url('../../../../img/site/icons/chevron_down_light.svg');
    }
}

.required .label::after {
    content: ' *';
    color: var(--colour-red-700);
}

.input-group {
    border: 1px solid var(--colour-grey-500);
    box-shadow: var(--shadow-xs);
    padding: var(--spacing-14px) var(--spacing-24px) var(--spacing-16px) var(--spacing-24px);
    border-radius: var(--rounded-xs);
}

.error-message {
    color: var(--colour-red-700);
    margin-top: var(--spacing-10px);
}

@include respond-to(md) {
    .input--half {
        grid-column: span 6;
    }
}
