.switch {
    --switch-background-image: url('../../../../img/site/icons/switch/dot.svg');
    --switch-border-colour: var(--colour-grey-400);
    --switch-background-colour: unset;

    display: flex;
    flex-direction: column-reverse; 
    gap: var(--spacing-18px);

    cursor: pointer;

    input { /* stylelint-disable-line selector-max-type */
        width: var(--spacing-48px);
        height: 26px;

        background-color: var(--switch-background-colour);

        background-image: var(--switch-background-image);
        background-repeat: no-repeat;
        background-position: left center;
        border: 1px solid var(--switch-border-colour);

        box-shadow: 0px 2px 4px 2px rgba(0, 0, 0, 0.06);

        border-radius: 2em;
        cursor: inherit;

        transition: background-position var(--speed-normal) var(--ease-in-out),
            background-color var(--speed-normal) var(--ease-in-out),
            border-color var(--speed-normal) var(--ease-in-out);

        appearance: none;

        /* stylelint-disable-next-line selector-max-type */
        &:checked {
            --switch-background-image: url('../../../../img/site/icons/switch/dot_checked.svg');
            --switch-border-colour: var(--colour-green-500);
            --switch-background-colour: var(--colour-green-500);
            background-position: right center;
        }
    }
}
