.form-card {
    border: 1px solid var(--colour-grey-400);
    background-color: var(--colour-white);
    border-radius: var(--rounded-xs);
    box-shadow: var(--shadow-xs);

    + .form-card {
        margin-top: var(--spacing-24px);
    }
}

.form-card__header {
    padding: var(--spacing-28px) var(--spacing-32px) var(--spacing-24px) var(--spacing-32px);

    border-bottom: 1px solid var(--colour-grey-500);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-card__title {
    font-weight: var(--font-sans-bold);
    font-size: var(--font-size-26px);
    line-height: 1.19;
    margin: 0;
}

.form-card__star {
    color: var(--colour-red-700);
}

.form-card__body {
    padding: var(--spacing-32px);
}

.form-card__footer {
    border-top: 1px solid var(--colour-grey-400);
    padding-top: var(--spacing-36px);
    margin-top: var(--spacing-36px);
}

.form-card__subnote {
    font-size: var(--font-size-14px);
    color: var(--colour-grey-600);
    margin-top: var(--spacing-36px);
}

.form-card--small .form-card__body {
    padding: var(--spacing-28px) var(--spacing-28px) var(--spacing-32px) var(--spacing-28px);
}

@include respond-to(tablet) {
    .form-card__body {
        padding: var(--spacing-36px) var(--spacing-56px) var(--spacing-56px) var(--spacing-56px);
    }

    .form-card__header {
        padding: var(--spacing-32px) var(--spacing-44px) var(--spacing-28px) var(--spacing-44px);
    }
}
