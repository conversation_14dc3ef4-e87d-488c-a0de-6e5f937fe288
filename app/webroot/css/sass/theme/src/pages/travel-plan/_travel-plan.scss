@import './feefo-card';
@import './opening-hours';
@import './person-counter';

.travel-plan {
    margin-top: var(--spacing-112px);
    margin-bottom: var(--spacing-80px);

    font-size: var(--font-size-16px);
    color: var(--colour-grey-900);
}

.travel-plan__intro {
    color: var(--colour-grey-700);
}

.travel-plan__header {
    margin-bottom: var(--spacing-80px);
}

.travel-plan__sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-12px);
    margin-top: var(--spacing-32px);
}

.travel-plan__phone {
    text-decoration: none;
    color: #565044;

    &:hover,
    &:focus {
        text-decoration: none;
    }
}

.travel-plan__address {
    font-style: normal;

    margin-bottom: var(--spacing-24px);

    p {
        line-height: 1.44;
    }
}

.travel-plan__budget {
    padding-bottom: 15px;
}

.travel-plan__numbers {
    display: inline-flex;
    gap: var(--spacing-32px) var(--spacing-56px);
    flex-wrap: wrap;
}

.travel-plan__title {
    width: 102px;
}

.travel-plan--lite #flashMessage {
    margin-bottom: 24px;
}

@include respond-to(tablet) {
    .travel-plan__sidebar {
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: var(--spacing-16px);
        align-items: start;
    }
}

@include respond-to(desktop) {
    .travel-plan {
        display: grid;
        gap: var(--grid-gutter);

        grid-template-columns: 8fr 4fr;
    }

    .travel-plan__sidebar {
        margin-top: 0;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-12px);
        align-items: stretch;
    }
}
