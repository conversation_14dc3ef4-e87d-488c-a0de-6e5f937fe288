/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
* CONTENTS...........................You're reading it!
* VARS...............................Colors, widths, heights, etc
* ANIMATIONS.........................Define keyframe animations
* SECTION CONTENT....................Default styles for section content
*/





/*------------------------------------*\
    $VARS
\*------------------------------------*/





/*------------------------------------*\
    $ANIMATIONS
\*------------------------------------*/

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}




/*------------------------------------*\
    $SECTION CONTENT
\*------------------------------------*/

.section-content-wrapper {
    margin-bottom: 30px;
}

    .section-content {

    }

        .section-content__link-header {
            @include adjust-font-size-to(16px);
            background-color: transparent;
            position: relative;
            display: block;
            padding: 15px 0;
            color: $bv_default;
            text-transform: uppercase;
            border: {
                style: solid;
                width: 1px 0 0;
                color: $bv_default;
            }
            cursor: pointer;
            width: 100%;

            &:after {
                @extend .icns-plus;
                position: absolute;
                top: 50%;
                right: 0;
                margin-top: -15px;
                display: block;
                content: '';

                .active & {
                    @extend .icns-minus;
                }
            }

            &:focus {
              outline: none;
            }

            br {
                content: ' ';
            }

        }

        .loading .section-content__link-header:after {
            transform-origin: 50% 50%;
            animation: spin 1s infinite linear;
        }

        .section-content:last-child .section-content__link-header {
            border-width: 1px 0 3px;
        }

        .section-content__inner {
            display: none;

            .active & {
                display: block;
                max-height: 25000px;
            }
        }

        .section-content--blog .section-content__inner {
            display: block;
            max-height: none;
        }

        .section-content__inner {
            max-height: 0;
            display: block;
            overflow: hidden;
            transition: 3s max-height ease;
        }



/*
* Media Queries
*/

@media (min-width: $tablet) {

    .section-content {
        display: none;

        &.active {
            display: block;
        }
    }

    .section-content.section-content--blog {
        display: block;
    }

        .section-content__link-header {
            display: none;
        }


}


@media (min-width: $desktop) {

    .section-content-wrapper {
        margin-bottom: 0;
    }

}
