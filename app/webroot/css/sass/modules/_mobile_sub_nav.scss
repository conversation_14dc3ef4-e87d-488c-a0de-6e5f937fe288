/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
* CONTENTS...........................You're reading it!
* VARS...............................Colors, widths, heights, etc
* MOBILESUBNAV.......................Default styles the mobile sub nav
*/





/*------------------------------------*\
    $VARS
\*------------------------------------*/






/*------------------------------------*\
    $MO<PERSON>SUBNAV
\*------------------------------------*/

.mobile-sub-nav {
    overflow: hidden;
    clear: both;

    .custom-select {
        max-width: 400px;

        select {
            font-size:20px;
            font-weight: bold;
            padding: .5em 1.9em .4em .5em;
            line-height:1.2;

            span {
                display: none;
            }
        }
    }


}


/*
* Media Queries
*/

@media (min-width: $tablet) {

    .mobile-sub-nav {

        .custom-select {
            max-width: 400px;

            select {
                font-size:24px;
                font-weight: normal;
            }
        }
    }

}


@media (min-width: $desktop) {

    .mobile-sub-nav {
        display: none;
    }


}
