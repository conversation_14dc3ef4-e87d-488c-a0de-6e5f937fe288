@charset "UTF-8";

/*------------------------------------*\
$TESTIMONIAL
\*------------------------------------*/

.testimonial-extract {
    @extend .clearfix;
    @include dashed-border(0 0 1px 0);

    blockquote {
        @extend .reset-bm;
        position: relative;
        // padding-right: 40px;
        // padding-left: 40px;

        &::before,
        &::after {
            display: none;
            position: absolute;
            top: 0;
            height: 40px;
            font-size: 100px;
            color: $bv_red;
            line-height: 100px;
            text-align: right;
        }

        &::before {
            left: 0;
            content: "“";
        }

        &::after {
            right: 0;
            content: "”";
        }
    }
}

.testimonial-extract--strip {
    // padding: 20px;
    border-bottom: none;
    background-image: image-url('layout/chrome/noise-lines.png');
    background-repeat: repeat;
    background-size: 60px 59px;
    border: 1px solid #fff;

    blockquote::before,
    blockquote::after {
        height: 30px;
        font-size: 70px;
        line-height: 60px;
    }
}

.testimonial-extract__inner {
    max-width: $lgDesktop;
    margin: 0 auto
}

.testimonial-extract__feefo {
    margin-bottom: 20px;
}

.testimonial-extract__content,
.testimonial-extract__content__author {
    color: $bv_default;
}

.testimonial-extract__content__author,
.testimonial-extract__read-more {

}

.testimonial-extract__content {
    @include adjust-font-size-to(16px, 1);

    p:first-child {
        margin-top: 5px;
    }
}

.testimonial-extract--strip .testimonial-extract__content {
    @include adjust-font-size-to(16px, 1);
}

.testimonial-extract__content__author {
    float: left;
    @include adjust-font-size-to(14px, 1, 18px);
    font-style: normal;
}

.testimonial-extract__read-more {
    clear: both;

    a {
        @extend .arrow-right-red-14;
    }
}

.testimonial-extract__date {
    font-style: italic;
}


@media (min-width: $tablet) {

    .testimonial-extract__feefo {
        float: left;
    }

    .testimonial-extract--home .testimonial-extract__content {
        // margin-left: 190px;
    }

}

@media (min-width: $lgDesktop) {

    .testimonial-extract__inner {
        padding: 0 10px;
    }

}
