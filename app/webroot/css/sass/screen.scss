/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
 * CONTENTS............You're reading it!
 * STYLE GUIE..........Follow these
 * VARS................Colors, widths, heights, etc
 * VENDOR IMPORTS......Set our reset defaults
 * SPRITES SETUP.......Configure and import sprites
 * MISC................Other stuff
 * LOCAL IMPORTS.......User defined styles
 */

/*------------------------------------*\
   $STYLE GUIDE
\*------------------------------------*/

// https://github.com/csswizardry/CSS-Guidelines
// https://github.com/mdo/code-guide

/*
.declaration-order {
  // @extends, @custom-mixins

  // Positioning

  // Box-model

  // Typography

  // Visual

  // Misc
}
*/






/*------------------------------------*\
    $VARS
\*------------------------------------*/

// Breakpoints
$tablet: 768px;
$desktop: 980px;
$lgDesktop: 1280px;

// Dimensions
$content_width: 1280px;
$destinations_body_width: calc(100% - 260px);

// Colors
$bv_red: #a80000;
$bv_orange: #be7000;
$bv_brown: #6c4000;
$bv_ruby: #ad1110;
$bv_default: #565044;

$module-light-bg: #f7f5f1;





/*------------------------------------*\
    $VENDOR IMPORTS
\*------------------------------------*/

// bower:scss
@import "bower_components/support-for/sass/_support-for.scss";
@import "bower_components/normalize-scss/sass/normalize/_import-now.scss";
// endbower

@import "h5bp";
@import "compass/css3";



/*------------------------------------*\
    $SPRITES SETUP
\*------------------------------------*/

@import "compass/utilities/sprites";

$icns-layout: smart;
$icns-sprite-dimensions: true;
@import "sprites/icns/*.png";
@include all-icns-sprites;

$logos-layout: smart;
$logos-sprite-dimensions: true;
@import "sprites/logos/*.png";
@include all-logos-sprites;





/*------------------------------------*\
    $LOCAL IMPORTS
\*------------------------------------*/

@import "functions";
@import "mixins";
@import "typography";
@import "classes";

@import "modules/modules";
@import "blog";
@import "campaigns";
@import "chrome";
@import "contact";
@import "faqs";
@import "holidays";
@import "home";
@import "lightbox";
@import "pages";
@import "press_releases";
@import "section";
@import "search";
@import "testimonials";
@import "whats_hot";





/*------------------------------------*\
    $MISC
\*------------------------------------*/

*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

hr {
  border-top: 1px solid adjust-color($bv_default, $alpha: -0.6);
}
