/*------------------------------------*\
    $CONTENTS - CHROME
\*------------------------------------*/
/**
* CONTENTS............You're reading it!
* STRUCTURE...........Holds it all together
* HEADER..............Header
* BREADCRUMB..........Breadcrumb navigation
* FOOTER..............Footer
* CONTACT.............Contact module
*/





/*------------------------------------*\
    $STRUCTURE
\*------------------------------------*/

body {
    background-image: image-url('layout/chrome/dust.png');
    background-repeat: repeat;
    background-size: 200px 150px;
}

.page-wrapper {
    margin-bottom: 84px;

    ~ {
        .page-footer {
            padding-bottom: 84px;
        }
    }

    &.page-wrapper--no-banner {
        margin-bottom: 0;

        .page-footer {
            padding-bottom: 52px;

            @media (min-width: 1440px) {
                padding-bottom: 0;
            }
        }
    }
}


/*------------------------------------*\
    $HEADER
\*------------------------------------*/

.page-header {
    position: relative;
    z-index: 10;
    background: $bv_red;
    @include box-shadow(0 7px 2px rgba(0,0,0,.3));
    color: #fff;

    a {
        color: #fff;
    }
}

.page-header__inner {
    @extend .page-wrapper__content--center;
    @extend .clearfix;
    position: relative;
    padding: 20px 18px;
}

.page-header__logo {
    @extend .ir;
    @extend .logos-bv-mobile;
    display: block;
    margin: 0 auto;
}

.nav-toggle {
    @extend .reset-btn;
    @include position(absolute, 12px auto auto 0px);
    padding: 15px;
}

.nav-toggle span {
    @extend .icns-menu;
    display: block;
}

.nav-wrapper {
    display: none;

    &.active {
        display: block;
    }
}

.primary-nav {

    ul {
        @extend .reset-bm;
        @extend .reset-list;
    }

    li {
        @include adjust-font-size-to(14px);
        padding: 14px 0;
        text-transform: uppercase;
        border-top: 1px dashed rgba(#fff,.3);
    }
}



.secondary-nav {
    margin-top: 18px;

    ul {
        @extend .reset-bm;
        @extend .reset-list;
    }

    li {
        @include adjust-font-size-to(15px);
        padding: 14px 0;
        line-height: 16px;
        text-transform: uppercase;
    }

    > ul > li {
        border-top: 1px dashed rgba(#fff,.3);
    }

    a:after {
        @extend .icns-arrow-white-right-13x13;
        display: none;
    }
}

.search-toggle {
    @extend .reset-btn;
    @extend .icns-search;
    @include position(absolute, 22px 16px auto auto);
}

.primary-search {
    display: none;
    margin-top: 18px;
    padding: 10px 0;
    border-top: 1px dashed rgba(#fff,.3);
    border-bottom: 1px dashed rgba(#fff,.3);

    input[type=text] {
        padding: 3px;
        width: 100%;
        color: $bv_default;
    }

    input[type=submit] {
        @extend .visuallyhidden;
    }

    button {
        @extend .icns-arrow-white-right-21x21;
        @extend .ir;
        @extend .reset-btn;
        float: right;
        margin: 3px 0 0 10px;
    }

    &.active {
        display: block;
    }
}

.primary-search__form {
    overflow: hidden;
}

.primary-search__input {
    width: auto;
    overflow: hidden;
}


/*
* Media Queries
*/

@media (min-width: $tablet) {

    .page-header {
        min-height: 90px;
    }

    .page-header__inner {
        padding: 14px 0 0;
    }

    .page-header__logo {
        @include position(absolute, 20px auto auto 18px);
        @include sprite($logos-sprites, bv-tablet, true);
    }

    .nav-toggle,
    .search-toggle {
        display: none;
    }

    .nav-wrapper {
        display: block;
    }

    .primary-nav {
        @include position(absolute, 20px 18px auto auto);
        display: block;

        li {
            display: inline;
            text-transform: none;
            border-top: none;

            &:last-child {
                margin-right: 0;
                padding-right: 0;

                &:after {
                    display: none;
                }
            }

            &:after {
                content: "|";
                margin: 0 7px;
            }
        }
    }

    .secondary-nav {
        display: block;
        margin: 85px 0 0 18px;

        li {
            padding: 0;
            display: inline-block;
            margin-right: 29px;
            vertical-align: bottom;
            border-top: none !important;

            &:last-child {
                margin-right: 0;
            }
        }

        a:after {
            @include position(relative, 2px auto auto 10px);
            display: inline-block;
            content: "";
        }

        > ul > li {
            position: relative;
            padding-bottom: 15px;
        }
    }

    .primary-search {
        @include position(absolute, 50px 18px auto auto);
        display: block;
        padding: 0;
        margin-top: 0;
        border-top: none;
        border-bottom: none;
    }

}

@media (min-width: $desktop) {

    .page-header__logo {
        @include position(absolute, 20px auto auto 0);
    }

    .primary-nav {
        @include position(absolute, 12px auto auto 340px);
    }

    .secondary-nav {
        @include position(absolute, 55px auto auto 340px);
        margin: 0;

        li {
            max-width: 128px;
        }

        li:nth-child(3) {
            display: none;
        }
    }

    .primary-search {

    }


}

@media (min-width: 1050px) {

    .secondary-nav {

        li:nth-child(3) {
            display: inline-block;
        }
    }

}

@media (min-width: $lgDesktop) {

    .secondary-nav {
        top: 54px;

        li {
            max-width: none;
        }

        li:nth-child(3) {
            display: inline-block;
        }
    }

}




/*------------------------------------*\
    $BREADCRUMB
\*------------------------------------*/

.breadcrumb {
    display: none;
    background-image: image-url('layout/chrome/noise-lines.png');
    background-repeat: repeat;
    background-size: 60px 59px;

    ul {
        @extend .reset-list;
        @extend .clearfix;
    }

    li {
        float: left;

        &::after {
            display: inline-block;
            margin: 0 5px 0 7px;
            content: "/";
        }

        &:last-child::after {
            content: "";
        }
    }

    a {
        color: $bv_brown;
    }
}

.breadcrumb__inner {
    @extend .page-wrapper__content--center;
    padding: 10px 18px;
}

/*
* Media Queries
*/

@media (min-width: $tablet) {

    .breadcrumb {
        display: block;
    }

}




/*------------------------------------*\
    $LEFT SIDEBAR
\*------------------------------------*/

.page-left-sidebar {
    @include position(absolute, 0px auto auto 0px);
    display: none;
    z-index: 10;
    width: 230px;

    h3 {
        color: $bv_brown;
    }

    .enews-module,
    .quote-module,
    .call-us-module {
        padding: 0 10px 10px;
        @include solid-border(0 0 1px);
    }

    .enews-module {
        border-bottom: none;
    }

    .enews-module__form {
        input[type=text],
        input[type=email] {
            width: 100%;
        }
    }

    .call-us-module a {
        @include adjust-font-size-to(18px);

        &::after {
            top: 0;
        }
    }
}

.page-left-sidebar--blog {
    @include position(absolute, 0px 0px auto auto);
}

.page-left-sidebar__tracker {
    position: relative;
}

/*
* Media Queries
*/

@media (min-width: $desktop) {

    .page-left-sidebar {
        display: block;
    }

}




/*------------------------------------*\
    $FOOTER
\*------------------------------------*/

.page-footer {
    background-image: image-url('layout/chrome/noise-lines.png');
    background-repeat: repeat;
    background-size: 60px 59px;
    border-top: 1px solid #fff;
}

.page-footer__inner {
    @extend .page-wrapper__content--center;
    @extend .clearfix;
    position: relative;
    padding: 10px 18px 40px;
}

.page-footer__about {
    @extend .clearfix;
    margin-bottom: 20px;

    a {
        color: $bv_red;
    }

    ul {
        @extend .reset-list;
    }
}

.page-footer__holidays {
    @extend .clearfix;
    margin-bottom: 20px;
}

.page-footer__holidays-inner ul > li {
    margin-bottom: 10px;
}

.page-footer__thirdp {
    clear: both;
    max-width: 365px;
}

.bv-community {
    display: flex;
    flex-wrap: wrap;
    row-gap: 12px;
}

.page-footer__nav {
    padding: 15px 0 0;
    clear: both;
    @include dashed-border(1px 0 0);

    display: flex;
    flex-direction: column;
    gap: 12px;
}

.page-footer__copyright {
    @extend .reset-bm;
    clear: both;
}



.thirdp-associations {
    @extend .clearfix;

    a {
        @extend .ir;
        margin: 15px 12px 0 0;
        display: inline-block;
        vertical-align: middle;
    }
}

.thirdp-associations__abta {
    @extend .logos-abta;
}

.thirdp-associations__iata {
    @extend .logos-iata;
}

.thirdp-associations__atol {
    @extend .logos-atol;
}

.thirdp-associations__discover-america {
    @extend .logos-discover-america;
}


.bv-community {
    @extend .reset-list;
    @extend .clearfix;
    margin-bottom: 20px;

    li {
        float: left;
    }

    a {
        @extend .ir;
        display: block;
        margin: 0 12px 0 0;
    }
}

.bv-community__wordpress {
    @extend .logos-wordpress-48;
    border-radius: 50%;
    overflow: hidden;
    background-color: #27769A;
}

.bv-community__facebook {
    @extend .logos-facebook-48;
    border-radius: 50%;
    overflow: hidden;
    background-color: #1877F2;
}

.bv-community__twitter {
    @extend .logos-twitter-48;
    border-radius: 50%;
    overflow: hidden;
    background-color: #000;
}

.bv-community__youtube {
    @extend .logos-youtube-48;
    border-radius: 50%;
    overflow: hidden;
    background-color: #FF0000;
}

.bv-community__linkedin {
    @extend .logos-linkedin-48;
    border-radius: 50%;
    overflow: hidden;
    background-color: #0966C2;
}

.bv-community__instagram {
    @extend .logos-instagram-48;
    border-radius: 50%;
    overflow: hidden;
}



.footer-nav {
    ul {
        @extend .reset-list;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    a {
        color: $bv_default;
    }
}

/*
* Media Queries
*/

@media (min-width: 450px) {

    .thirdp-associations__discover-america {
        float: right;
        display: block;
    }

}

@media (min-width: 500px) {

    .page-footer__copyright {
        text-align: right;
    }

    .page-footer__nav {
        flex-direction: row;
        justify-content: space-between;
    }

}

@media (min-width: $tablet) {
    .page-footer__holidays-inner > ul > li {
        float: left;
        width: 50%;
    }

    .thirdp-associations {
        float: right;
        text-align: right;

        a {
            margin: 0 0 0 12px;
        }
    }

    .bv-community {
        float: left;

        li {
            float: left;
        }

        a {
            margin: 0 12px 12px 0;
        }
    }
}


@media (min-width: 880px) {
    .footer-nav {
        ul {
            flex-direction: row;
            gap: 16px;
        }
    }
}

@media (min-width: $desktop) {

    .page-footer__about {
        min-height: 320px;
    }

    .page-footer__holidays-inner > ul > li {
        width: 33.3333%;
    }

}


@media (min-width: $lgDesktop) {

    .page-footer__holidays-inner {

        > ul > li {
            float: left;
            width: 40%;
            width: calc(50% - 10px);
        }
    }

    .page-footer__holidays-inner > ul > li {
        width: 25%;
    }

}



/*------------------------------------*\
    $CONTACT
\*------------------------------------*/

.page-wrapper__contact-module {
    @extend .clearfix;
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 12;
    background: rgba(#eee,0.9);
    border-top: 1px solid rgba($bv_red,0.4);

    h3 {
        @include adjust-font-size-to(16px, 1);
        margin: 5px 0;
    }

    .call-us-module p {
        margin: 0 0 5px;

        &:first-child {
            margin-top: 10px;
        }

        &:last-child {
            margin-bottom: 10px;

            span {
                display: none;
            }
        }
    }

    .call-us-module h3 {
        display: none;
    }

    .quote-module {
        float: right;

        a span {
            display: none;
        }
    }

    .quote-module__intro,
    .quote-module__bonded {
        display: none;
    }

    .enews-module {
        max-width: 800px;

        input[type=text],
        input[type=email] {
            float: left;
            margin-right: 10px;
            width: calc(50% - 5px);
        }

        input[name=last_name] {
            margin-right: 0;
        }

        .enews-module__form button {
            clear: none;
        }
    }

    .enews-module__our-ideas {
        display: none;
    }

}

.page-wrapper__contact-module-inner {
    padding: 0 10px;
    overflow: hidden;
    max-width: $lgDesktop;
    margin: 0 auto;
}

/*
* Media Queries
*/

@media (min-width: 380px) {

    .page-wrapper__contact-module .call-us-module p:last-child span {
        display: inline;
    }

}

@media (min-width: $tablet) {

    .page-wrapper__contact-module {

        h3 {
            @include adjust-font-size-to(18px, 1);
            margin: 10px 0;
        }

        .enews-module {
            float: left;
            margin-bottom: 10px;
            width: calc(100% - 230px);

            input[type=text],
            input[type=email] {
                width: calc(33% - 34px);
            }

            input[name=last_name] {
                margin-right: 10px;
            }
        }

        .call-us-module {
            padding-top: 12px;
        }

        .call-us-module p:first-child {
            margin-top: 0;
        }

        .call-us-module h3 {
            display: block;
        }

    }

}


@media (min-width: $desktop) {

    .page-wrapper__contact-module {

        .enews-module {


            input[type=text],
            input[type=email] {
                width: calc(33% - 37px);
            }

            input[name=last_name] {
                margin-right: 10px;
            }

        }

    }

}

/*
 * Feefo Widget
*/

.feefo-widget {
    margin-top: 25px;
    margin-bottom: 25px;
}
