/*------------------------------------*\
    $CONTENTS
\*------------------------------------*/
/**
* CONTENTS...........................You're reading it!
* VARS...............................Colors, widths, heights, etc
* SECTION............................Default styles for destinations, itineraries etc
* NAVIGATION.........................Section tabs
* OVERVIEW...........................Destination overview page
* INDEX..............................Index for itineraries, activities
* VIEW...............................Detail page for itineraries, activities
* ACCOMMODATION......................Accommodation
* ITINERARIES ...................... Itineraries
* VIDEOS.............................Video Gallery
* IMAGES.............................Images Gallery
* RIGHT SIDEBAR......................Right side bar
* RELATED HOLIDAYS & ACTIVITIES......Related records
*/





/*------------------------------------*\
    $VARS
\*------------------------------------*/

$body-nav-tab-height: 80px;





/*------------------------------------*\
    $SECTION
\*------------------------------------*/

.page-content-body {
    padding: 0 18px;

    .page-content-body__inner {
        @extend .page-wrapper__content--center;
        @extend .clearfix;
        position: relative;
    }

    .image-and-map {
        display: none;
        position: relative;
        z-index: 11;

        &--itinerary {
            display: block;
        }
    }

        .image-and-map__image {
            width: 355px;
            height: 340px;
            overflow: hidden;
        }

        .image-and-map__image--full-image {
            width: 100%;
            height: auto;
        }

        .image-and-map__map-wrapper {
            width: calc(100% - 365px);
            height: 340px;

            &--full-width {
                width: 100%;
                max-width: none;
            }
        }

        .image-and-map__map-wrapper--fixed {
            background-color: #fff;
            border-right: 0;
            border: 1px solid rgba($bv_default, 0.2);
            bottom: 120px;
            left: 100%;
            padding: 10px 0 10px 10px;
            position: fixed;
            top: 10%;
            transition: 0.6s;
            width: 80%;
            z-index: 100000;

            &.active {
               left: 20%;
            }
        }

        .image-and-map__hide {
            display: none;
        }

        .image-and-map__map-wrapper--fixed .image-and-map__hide {
            @extend .arrow-right-white-13;
            background-color: $bv_red;
            border: none;
            color: #fff;
            display: inline-block;
            padding: 0 10px 2px;
            font-size: 15px;
            line-height: 2em;
            outline: 0;
            position: absolute;
            top: 100px;
            left: -1px;
        }
}

/*
* Media Queries
*/

@media (min-width: $tablet) {

    .page-content-body {
        padding: 0;

        .image-and-map {
            display: block;
            margin: 0 0 0 18px;
        }
    }

}

@media (min-width: $desktop) {

    .page-content-body {
        min-height: 900px;

        .image-and-map {
            float: right;
            margin: 0;
            width: $destinations_body_width;
        }
    }

}






/*------------------------------------*\
    $NAVIGATION
\*------------------------------------*/

.destinations-nav {
    display: none;
    position: relative;
    top: -10px;
    padding-top: 30px;
    background-image: image-url('layout/chrome/noise-lines.png');
    background-repeat: repeat;
    background-size: 60px 59px;

    .destinations-nav__inner {
        @extend .page-wrapper__content--center;
        @extend .clearfix;
    }

    ul {
        @extend .reset-list;
        @extend .clearfix;

        margin-left: 28px;
        border-top: 1px solid $bv_default;
        border-bottom: 3px solid $bv_default;
    }

    li {
        float: left;

        &:first-child button {
            border-left: 1px dashed $bv_default;
        }
    }

    .active {
        margin-bottom: -3px;

        button {
            height: $body-nav-tab-height + 3;
            background-image: image-url('layout/chrome/dust.png');
            background-repeat: repeat;
            background-size: 200px 150px;
        }
    }

    button {
        display: block;
        height: $body-nav-tab-height;
        background: none;
        padding: 0 15px;
        border: {
            color: $bv_default;
            width: 0 1px 0 0;
            style: none dashed;
        }
        color: $bv_default;
        @include adjust-font-size-to(16px, 1);
        text-transform: uppercase;
    }

    button:focus {
        outline: none;
    }
}

/*
* Media Queries
*/

@media (min-width: $tablet) {

    .destinations-nav {
        display: block;
    }

}


@media (min-width: $desktop) {

    .destinations-nav {

        ul {
            float: right;
            width: $destinations_body_width;
            margin-left: 0;
        }
    }

}






/*------------------------------------*\
    $OVERVIEW
\*------------------------------------*/

.page-content-body__content {
    @extend .clearfix;
    @include trailer(2);
}

    .content-blocks {

        .content-block:last-child {
            margin-bottom: 0;
        }
    }

/*
* Media Queries
*/

@media (min-width: $tablet) {

    .page-content-body__content {
        padding: 0 18px;
    }

        .content-blocks {
            width: auto;
            overflow: hidden;
        }

}


@media (min-width: $desktop) {

    .page-content-body__content {
        float: right;
        min-height: 680px;
        width: $destinations_body_width;
        padding: 0;
    }

    .page-content-body__content--blog {
        float: left;
    }

}






/*------------------------------------*\
    $INDEX
\*------------------------------------*/

.content-blocks.content-blocks--index {

    .content-block {
        @include dashed-border(0 0 1px);
        padding-bottom: 20px;

        h2 {
            @include adjust-font-size-to(21px, 1.5);
            @include trailer(.5, 21px);
        }

        h3 {
            @include adjust-font-size-to(16px, 1.5);
        }
    }
}

.content-block__text__location {
    color: $bv_red;
    @include adjust-font-size-to(16px, 1.5);
}

.itineraries {
    padding-top: 30px;
}




/*------------------------------------*\
    $VIEW
\*------------------------------------*/

.sub-section-header {
    h2 {
        margin-bottom: 0;
        @include adjust-font-size-to(26px);
        color: $bv_brown;
    }

    .sub-section-header__price {
        margin-top: 0;
        color: $bv_red;
        @include adjust-font-size-to(17px, 1.5);
        text-transform: uppercase;
    }
}





/*------------------------------------*\
    $ACCOMMODATION
\*------------------------------------*/

.content-blocks--index.content-block--accommodation .content-block {
    border-bottom: none;
    padding-bottom: 0;

    .content-block__text {
        > h2 {
            margin-bottom: 0;
        }
    }
}


/*
* Media Queries
*/

@media (min-width: $tablet) {

    .content-blocks--accommodation {
        width: calc(100% - 260px);
        float: left;
    }

}

@media (min-width: $lgDesktop) {


}





/*------------------------------------*\
    $ITINERARIES
\*------------------------------------*/

.content-blocks.content-blocks--itinerary {
    .content-block:first-child {
        margin-top: 0;
    }
}

.content-blocks.content-blocks--itinerary-day {
    max-width: 710px;
}





/*------------------------------------*\
    $VIDEOS
\*------------------------------------*/

.youtube-videos {
    @extend .clearfix;
    margin-top: 30px;
}

    .youtube-videos__video {
        margin: 0 0 40px;
    }

        .youtube-videos__iframe-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding-top: 71%;
        }
            .youtube-videos__iframe {
                position: absolute;
                top: 0;
                left: 0;
                /* right: 0; */
                /* bottom: 0; */
                width: 100%;
                height: 100%;
            }

            .youtube-videos__link a {
                @extend .arrow-right-red-14;
            }

/*
* Media Queries
*/

@media (min-width: $tablet) {

    .youtube-videos__video {
        float: left;
        width: 40%;
        margin: 0 20px 40px 0;

        &:nth-child(odd) {
            clear: left;
        }

        &:nth-child(even) {
            margin-right: 0;
        }
    }

}






/*------------------------------------*\
    $IMAGES
\*------------------------------------*/

.image-viewer.image-viewer--section-images {
    width: 616px;
    margin-top: 30px;

    .image-viewer__wrapper__images__image {
        margin-right: 10px;
    }
}





/*------------------------------------*\
    $RIGHT SIDEBAR
\*------------------------------------*/

.page-right-sidebar {
    display: none;
    float: right;
    width: 230px;
    margin-top: 30px;
    padding: 0 10px;

    .enews-module__form input[type=text],
    .enews-module__form input[type=email] {
        width: 100%;
    }
}

/*
* Media Queries
*/

@media (min-width: $tablet) {

    .page-right-sidebar {
        display: block;
        margin-left: 22px;
    }

    .page-right-sidebar .testimonial-extract__content {
        padding-left: 0;

        &::before {
            display: none;
        }
    }

}


@media (min-width: $desktop) {

    .page-right-sidebar {

    }

}







/*------------------------------------*\
    $RELATED HOLIDAYS & ACTIVITIES
\*------------------------------------*/

.holidays-and-activities {
    @extend .page-wrapper__content--center;
    @extend .clearfix;
    @include leader(2);
    @include trailer(2);
    padding: 0 18px;
}

/*
* Media Queries
*/

@media (min-width: $desktop) {

    .holidays-and-activities {
        padding: 0 10px;
    }

}
