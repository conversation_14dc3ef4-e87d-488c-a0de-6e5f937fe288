<?php return array(
    'root' => array(
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '00936e3ced9960965fd9f738e3915b2ac47db966',
        'name' => 'yoast/wordpress-seo',
        'dev' => false,
    ),
    'versions' => array(
        'composer/installers' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '*******',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'reference' => 'c29dc4b93137acb82734f672c37e029dfbd95b35',
            'dev_requirement' => false,
        ),
        'yoast/whip' => array(
            'pretty_version' => '2.0.0',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yoast/whip',
            'aliases' => array(),
            'reference' => '5cfd9c3b433774548ec231fe896d5e85d17ed0d1',
            'dev_requirement' => false,
        ),
        'yoast/wordpress-seo' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '00936e3ced9960965fd9f738e3915b2ac47db966',
            'dev_requirement' => false,
        ),
    ),
);
