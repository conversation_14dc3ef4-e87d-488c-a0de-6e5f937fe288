// Parse UTM parameters from the URL
function getUTMParams() {
  const params = new URLSearchParams(window.location.search);
  const utmParams = {};

  // Collect only UTM parameters if present in the query string
  ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(param => {
      if (params.has(param)) {
          utmParams[param] = params.get(param);
      }
  });

  return Object.keys(utmParams).length ? utmParams : null;
}

// Function to sanitize and URL-encode UTM parameters
function sanitizeAndEncodeUTMParams(utmParams) {
    const regex = /^[a-zA-Z0-9_\%\-\+&,\[\]\(\): ]+$/;
    const sanitizedUTM = {};

    Object.keys(utmParams).forEach(key => {
        let value = utmParams[key];
        
        // Convert value to string if it isn't already
        value = String(value);
        
        // Keep only valid characters
        const sanitizedValue = [...value]
            .filter(char => regex.test(char))
            .join('');
        
        // URL-encode the sanitized value
        sanitizedUTM[key] = encodeURIComponent(sanitizedValue);
    });

    return sanitizedUTM;
}

// Function to convert object to URL-encoded string
function toURLEncodedString(params) {
    return Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${params[key]}`)
        .join('&');
}

// Function to set a cookie with URL-encoded value and expiration
function setCookie(name, value, days) {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    const expires = `; expires=${date.toUTCString()}`;
    const secureFlag = location.protocol === 'https:' ? '; Secure' : '';
    document.cookie = `${name}=${value}${expires}; path=/${secureFlag}`;
}

// Function to get a cookie by name
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
        return parts.pop().split(';').shift();
    }
    return null;
}

// Check for UTM parameters in the URL and set the bv_utm cookie if not already set
(function() {
  const utmParams = getUTMParams();
  const sanitizedParams = utmParams ? sanitizeAndEncodeUTMParams(utmParams) : null;
  if (sanitizedParams && !getCookie('bv_utm')) {
      const encodedParams = toURLEncodedString(sanitizedParams);
      setCookie('bv_utm', encodedParams, 7); // Store the URL-encoded UTM params
      console.log('Setting cookie');
      console.log(encodedParams);
  }

  // Populate a hidden field in the contact form if the bv_utm cookie is set
  document.addEventListener("DOMContentLoaded", () => {
      const bvUtmCookie = getCookie('bv_utm');
      if (bvUtmCookie) {
          console.log("Getting cookie");
          console.log(bvUtmCookie);
          let hiddenField = document.querySelector('input[name*="[utm]"]');
          if (hiddenField) {
              console.log("Hidden field found: ", hiddenField);
              hiddenField.value = bvUtmCookie; // Store as URL-encoded string in form field
          }
      }
  });
})();
