import {createApp} from 'vue/dist/vue.esm-bundler';
import RangeSlider from './components/RangeSlider.vue';
import Counter from './components/Counter.vue';
import TravelPlanDestination from './components/TravelPlanDestination.vue';

createApp()
  .component('range-slider', RangeSlider)
  .component('counter', Counter)
  .component('travel-plan-destination', TravelPlanDestination)
  .mount('#app');

// Load the rest of the site JS after V<PERSON> has finished executing so it does not conflict with Prototype.js
if (typeof window.loadOtherJs === 'function') {
  window.loadOtherJs();
}
