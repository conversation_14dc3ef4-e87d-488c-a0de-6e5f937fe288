<template>
    <fieldset class="form__row">
        <legend class="label">Region <span class="form-card__star">*</span></legend>
        <div class="input select input--half required" :class="{error: errors?.destination_country}">
            <select @change="onCountryChange" v-model="selectedCountry" name="data[TravelPlan][destination_country]">
                <option value="">Country</option>
                <option v-for="country in Object.keys(countries)" :value="country">{{ countries[country] }}</option>
            </select>
            <div class="error-message" v-if="errors?.destination_country">{{ errors.destination_country }}</div>
        </div>

        <div class="input select input--half required" :class="{error: errors?.destination_region}">
            <select v-model="selectedRegion" :disabled="selectedCountry === ''" name="data[TravelPlan][destination_region]">
                <option value="">Region</option>
                <option v-for="key in Object.keys(regionsToShow)" :value="key">{{ regionsToShow[key] }}</option>
            </select>
            <div class="error-message" v-if="errors?.destination_region">{{ errors.destination_region }}</div>
        </div>
    </fieldset>
</template>

<script>
export default {
    name: 'TravelPlanDestination',
    props: {
        countries: Object,
        regions: Object,
        errors: Object,
        data: Object,
    },
    data() {
        return {
            selectedCountry: this.data?.destination_country ?? '',
            selectedRegion: this.data?.destination_region ?? '',
        };
    },
    computed: {
        regionsToShow() {
            if (!this.selectedCountry) {
                return [];
            }

            return this.regions[this.selectedCountry];
        }
    },
    methods: {
        onCountryChange() {
            if (!Object.keys(this.regionsToShow).includes(this.selectedRegion)) {
                this.selectedRegion = '';
            }
        }
    }
};
</script>
