<template>
    <fieldset class="input range" :class="{'required': required}">
        <legend class="label">{{ label }}</legend>

        <div class="range__values">{{ displayFromValue }} - {{ displayToValue }}</div>

        <input :name="fromName" v-model="fromVal" :style="{background: gradient}" :onInput="onFromChange" class="range__input" :min="min" :max="max" :step="step" type="range" />

        <input :name="toName" v-model="toVal" :onInput="onToChange" class="range__input range__input--to" :min="min" :max="max" :step="step" type="range" />
    </fieldset>
</template>

<script>
export default {
    name: 'RangeSlider',
    props: {
        label: String,
        min: Number,
        max: Number,
        step: Number,
        fromName: String,
        toName: String,
        minValue: Number,
        maxValue: Number,
        required: {
            type: Boolean,
            default: false,
        },
        addPlus: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            fromVal: this.minValue !== undefined ? this.minValue : this.min,
            toVal: this.maxValue !== undefined ? this.maxValue : this.max,
        };
    },
    methods: {
        onFromChange() {
            if (parseInt(this.fromVal, 10) >= parseInt(this.toVal, 10)) {
                this.fromVal = this.toVal;
            }
        },
        onToChange() {
            if (parseInt(this.toVal, 10) <= parseInt(this.fromVal, 10)) {
                this.toVal = this.fromVal;
            }
        },
        formatNumber(val) {
            return new Intl.NumberFormat('en-GB', { style: 'currency', currency: 'GBP', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(val);
        }
    },
    computed: {
        displayFromValue() {
            const value = this.formatNumber(this.fromVal);

            return `${value}`;
        },
        displayToValue() {
            const value = this.formatNumber(this.toVal);

            if (!this.addPlus || parseInt(this.toVal) !== this.max) {
                return value;
            }

            return `${value}+`;
        },
        gradient() {
            const rangeDistance = this.max - this.min;
            const fromPosition = parseInt(this.fromVal, 10) - this.min;
            const toPosition = parseInt(this.toVal, 10) - this.min;
            return `linear-gradient(
            to right,
            #E7E6E4 0%,
            #E7E6E4 ${(fromPosition/rangeDistance)*100}%,
            #A80000 ${(fromPosition/rangeDistance)*100}%,
            #A80000 ${(toPosition/rangeDistance)*100}%,
            #E7E6E4 ${(toPosition/rangeDistance)*100}%,
            #E7E6E4 100%)`;
        }
    }
};
</script>
