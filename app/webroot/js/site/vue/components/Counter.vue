<template>
    <div class="counter">
        <input :id="id" :name="name" type="hidden" :value="counterValue" />

        <button title="Reduce" aria-label="Reduce" :disabled="counterValue <= min" class="counter__button" @click="reduceValue" type="button">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20"><mask id="a" width="20" height="20" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type:alpha"><path fill="#D9D9D9" d="M.104.104h19.791v19.791H.104z"/></mask><g mask="url(#a)"><path fill="currentColor" d="M5.052 10.825a.798.798 0 0 1-.587-.237.798.798 0 0 1-.237-.588c0-.233.079-.43.237-.587a.798.798 0 0 1 .587-.237h9.896c.233 0 .43.079.587.237a.798.798 0 0 1 .237.587c0 .234-.079.43-.237.588a.798.798 0 0 1-.587.237H5.052Z"/></g></svg>
        </button>
        <span class="counter__value">{{ counterValue }}</span>
        <button title="Increase" aria-label="Increase" class="counter__button" @click="increaseValue" type="button">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 21 20"><mask id="a" width="21" height="20" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type:alpha"><path fill="#D9D9D9" d="M.261.104h19.791v19.791H.261z"/></mask><g mask="url(#a)"><path fill="currentColor" d="M9.332 10.825H5.21a.798.798 0 0 1-.587-.237.798.798 0 0 1-.237-.588c0-.234.079-.43.237-.588a.798.798 0 0 1 .587-.237h4.123V5.052c0-.233.08-.43.237-.587a.798.798 0 0 1 .588-.237c.234 0 .43.079.588.237a.798.798 0 0 1 .237.587v4.123h4.123c.233 0 .43.08.587.237a.798.798 0 0 1 .237.588c0 .234-.079.43-.237.588a.798.798 0 0 1-.587.237h-4.123v4.123c0 .233-.08.43-.237.587a.798.798 0 0 1-.588.237.798.798 0 0 1-.588-.237.798.798 0 0 1-.237-.587v-4.123Z"/></g></svg>
        </button>
    </div>
</template>


<script>
export default {
    name: 'Counter',
    props: {
        name: String,
        id: String,
        value: Number,
        min: {
            type: Number,
            default: 0,
        }
    },
    data() {
        return {
            counterValue: this.value !== undefined ? this.value : this.min,
        };
    },
    methods: {
        reduceValue() {
            this.counterValue -= 1;
        },
        increaseValue() {
            this.counterValue += 1;
        }
    }
};
</script>
