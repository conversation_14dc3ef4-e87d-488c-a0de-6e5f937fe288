/* global $$ */
(function(b, o, n, v, oo, y, a, g, e) {
    'use strict';

    var scripts = [
        '//maps.googleapis.com/maps/api/js?v=3&&callback=App.initGMaps&key=' + b.<PERSON><PERSON>,
        'https://www.youtube.com/iframe_api',
        // '//platform.twitter.com/',
        // '/proxy/twitter/?resource=' + encodeURIComponent('widgets.js'),
        // '//connect.facebook.net/en_US/sdk.js#xfbml=1&appId=1377248055823193&version=2.5',
        // '/proxy/facebook/?resource=' + encodeURIComponent('en_GB/sdk.js#xfbml=1&version=v2.5'),
        '//connect.facebook.net/en_GB/sdk.js#xfbml=1&version=v2.5',
        '//cdn.optimizely.com/js/2132681994.js',
        // 'api.feefo.com/api/javascript/bon-voyage',
    ];

    e = o.createElement('div');
    e.id = v;
    o.getElementsByTagName('body')[0].appendChild(e);

    for (var i = scripts.length - 1; i >= 0; i--) {
        g = o.createElement(n);
        g.async = true;
        g.src = scripts[i];
        e.appendChild(g);
    }

    //Wistia
    g = o.createElement(n);
    g.async = true;
    //fast.wistia.net/assets/external/E-v1.js';
    g.src = '/proxy/wistia/?resource=' + encodeURIComponent('assets/external/E-v1.js');
    e.appendChild(g);
    $(g).observe('load', function () {
        window._wq = window._wq || [];
        _wq.push({ '_all': function(video) {}});
    });

    //YouTube iFrame API
    b.onYouTubeIframeAPIReady = function () {
        $$('.js-youtube-embed').each(function (e) {
            new b.App.Video(e, {
                type: 'youtube'
            });
        });
    };


    // AddThis
    // g = o.createElement(n);
    // g.async = true;
    // g.src = 'https://s7.addthis.com/js/300/addthis_widget.js#pubid=ra-524c8e8e7a971bde';
    // // g.src = '/proxy/addthis/?resource=' + encodeURIComponent('js/300/addthis_widget.js#pubid=ra-524c8e8e7a971bde');
    // e.appendChild(g);
    // $(g).observe('load', function () {
    //     b.addthis.layers({
    //         theme : 'transparent',
    //         share : {
    //             position : 'left',
    //             services : 'twitter,facebook,google_plusone_share,email,more',
    //             offset: {
    //                 top: '400px'
    //             }
    //         },
    //         responsive : {
    //             maxWidth: '1100px',
    //             minWidth: '0px'
    //         }
    //     });
    // });

    // Remarketing
    var remarketingParams = {
        google_conversion_id: 1030719956,
        google_custom_params: window.google_tag_params,
        google_remarketing_only: true
    };

    var optionalParams = [
        'google_conversion_language',
        'google_conversion_format',
        'google_conversion_color',
        'google_conversion_label',
        'google_conversion_value'
    ];

    optionalParams.each(function (param) {
        if (typeof window[param] !== 'undefined') {
            remarketingParams[param] = window[param];
        }
    });
    g = o.createElement(n);
    g.async = true;
    g.src = '//www.googleadservices.com/pagead/conversion_async.js';
    e.appendChild(g);
    $(g).observe('load', function () {
        window.google_trackConversion(remarketingParams);
    });

    // Google Tag Manager
    b[oo]=b[oo]||[];
    b[oo].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
    });
    a = o.getElementsByTagName(n)[0];
    y = o.createElement(n);
    var dl = oo != 'dataLayer' ? '&l=' + oo : '';
    y.async = true;
    y.src = '//www.googletagmanager.com/gtm.js?id=GTM-N7J7423' + dl;
    a.parentNode.insertBefore(y,a);

    // Facebook tracking
    if (b.fbq)
        return;
    var z = b.fbq = function() {
        z.callMethod ?
        z.callMethod.apply(z, arguments) : z.queue.push(arguments)
    }
    ;
    if (!b._fbq)
        b._fbq = z;
    z.push = z;
    z.loaded = !0;
    z.version = '2.0';
    z.queue = [];
    g = o.createElement(n);
    g.async = !0;
    g.src = '//connect.facebook.net/en_US/fbevents.js';
    e.appendChild(g);

}(window, document, 'script', 'async-scripts', 'dataLayer'));

// Optimizely Universal Analytics Integration
window.optimizely = window.optimizely || [];
window.optimizely.push('activateUniversalAnalytics');

window.fbq('init', '1547956112196568');
window.fbq('track', 'PageView');
