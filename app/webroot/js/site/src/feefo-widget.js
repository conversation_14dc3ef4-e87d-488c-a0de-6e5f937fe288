/* global Ajax, Element, Modernizr */

var App = window.App || {};

App.FeefoWidget = (function () {

    'use strict';

    var FeefoWidget = function ($surface) {
        this.surface = $surface;

        this.init();
    };

    FeefoWidget.prototype.init = function () {
        var _this = this;

        this.surface.addEventListener('load', function() {
            _this.checkHeight();
        });

        window.addEventListener('resize', function () {
            _this.updateHeight();
        });
    };

    FeefoWidget.prototype.updateHeight = function () {
        var height = this.surface.contentDocument.body.offsetHeight;

        if (!height) {
            return false;
        }

        this.surface.style.height = height + 'px';

        return true;
    };
        
    FeefoWidget.prototype.checkHeight = function () {
        var _this = this;

        var timer = setInterval(function() {
            var successful = _this.updateHeight();
            
            if (successful) {
                clearInterval(timer);
            }
        }, 50);
    }

    return FeefoWidget;

})();
