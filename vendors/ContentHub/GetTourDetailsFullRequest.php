<?php

namespace ContentHub;

class GetTourDetailsFullRequest
{

    /**
     * @var string $securityKey
     */
    protected $securityKey = null;

    /**
     * @var string $sellingCompany
     */
    protected $sellingCompany = null;

    /**
     * @var string $tourCode
     */
    protected $tourCode = null;

    /**
     * @param string $securityKey
     * @param string $sellingCompany
     * @param string $tourCode
     */
    public function __construct($securityKey, $sellingCompany, $tourCode)
    {
      $this->securityKey = $securityKey;
      $this->sellingCompany = $sellingCompany;
      $this->tourCode = $tourCode;
    }

    /**
     * @return string
     */
    public function getSecurityKey()
    {
      return $this->securityKey;
    }

    /**
     * @param string $securityKey
     * @return \ContentHub\GetTourDetailsFullRequest
     */
    public function setSecurityKey($securityKey)
    {
      $this->securityKey = $securityKey;
      return $this;
    }

    /**
     * @return string
     */
    public function getSellingCompany()
    {
      return $this->sellingCompany;
    }

    /**
     * @param string $sellingCompany
     * @return \ContentHub\GetTourDetailsFullRequest
     */
    public function setSellingCompany($sellingCompany)
    {
      $this->sellingCompany = $sellingCompany;
      return $this;
    }

    /**
     * @return string
     */
    public function getTourCode()
    {
      return $this->tourCode;
    }

    /**
     * @param string $tourCode
     * @return \ContentHub\GetTourDetailsFullRequest
     */
    public function setTourCode($tourCode)
    {
      $this->tourCode = $tourCode;
      return $this;
    }

}
