<?php

namespace ContentHub;

class UploadTourInfoResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    /**
     * @var boolean $successful
     */
    protected $successful = null;

    /**
     * @param MessageContext $messageContext
     * @param boolean $successful
     */
    public function __construct($messageContext, $successful)
    {
      $this->messageContext = $messageContext;
      $this->successful = $successful;
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\UploadTourInfoResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getSuccessful()
    {
      return $this->successful;
    }

    /**
     * @param boolean $successful
     * @return \ContentHub\UploadTourInfoResponse
     */
    public function setSuccessful($successful)
    {
      $this->successful = $successful;
      return $this;
    }

}
