<?php

namespace ContentHub;

class AddressType
{

    /**
     * @var string $Line1
     */
    protected $Line1 = null;

    /**
     * @var string $Line2
     */
    protected $Line2 = null;

    /**
     * @var string $Line3
     */
    protected $Line3 = null;

    /**
     * @var string $City
     */
    protected $City = null;

    /**
     * @var string $Region
     */
    protected $Region = null;

    /**
     * @var string $Postcode
     */
    protected $Postcode = null;

    /**
     * @var string $Country
     */
    protected $Country = null;

    /**
     * @param string $Line1
     * @param string $City
     * @param string $Region
     * @param string $Postcode
     * @param string $Country
     */
    public function __construct($Line1, $City, $Region, $Postcode, $Country)
    {
      $this->Line1 = $Line1;
      $this->City = $City;
      $this->Region = $Region;
      $this->Postcode = $Postcode;
      $this->Country = $Country;
    }

    /**
     * @return string
     */
    public function getLine1()
    {
      return $this->Line1;
    }

    /**
     * @param string $Line1
     * @return \ContentHub\AddressType
     */
    public function setLine1($Line1)
    {
      $this->Line1 = $Line1;
      return $this;
    }

    /**
     * @return string
     */
    public function getLine2()
    {
      return $this->Line2;
    }

    /**
     * @param string $Line2
     * @return \ContentHub\AddressType
     */
    public function setLine2($Line2)
    {
      $this->Line2 = $Line2;
      return $this;
    }

    /**
     * @return string
     */
    public function getLine3()
    {
      return $this->Line3;
    }

    /**
     * @param string $Line3
     * @return \ContentHub\AddressType
     */
    public function setLine3($Line3)
    {
      $this->Line3 = $Line3;
      return $this;
    }

    /**
     * @return string
     */
    public function getCity()
    {
      return $this->City;
    }

    /**
     * @param string $City
     * @return \ContentHub\AddressType
     */
    public function setCity($City)
    {
      $this->City = $City;
      return $this;
    }

    /**
     * @return string
     */
    public function getRegion()
    {
      return $this->Region;
    }

    /**
     * @param string $Region
     * @return \ContentHub\AddressType
     */
    public function setRegion($Region)
    {
      $this->Region = $Region;
      return $this;
    }

    /**
     * @return string
     */
    public function getPostcode()
    {
      return $this->Postcode;
    }

    /**
     * @param string $Postcode
     * @return \ContentHub\AddressType
     */
    public function setPostcode($Postcode)
    {
      $this->Postcode = $Postcode;
      return $this;
    }

    /**
     * @return string
     */
    public function getCountry()
    {
      return $this->Country;
    }

    /**
     * @param string $Country
     * @return \ContentHub\AddressType
     */
    public function setCountry($Country)
    {
      $this->Country = $Country;
      return $this;
    }

}
