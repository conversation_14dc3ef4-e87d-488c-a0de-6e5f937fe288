<?php

namespace ContentHub;

class Price
{

    /**
     * @var string $PassengerType
     */
    protected $PassengerType = null;

    /**
     * @var string $CurrencyCode
     */
    protected $CurrencyCode = null;

    /**
     * @var float $AmountFrom
     */
    protected $AmountFrom = null;

    /**
     * @var float $AmountTo
     */
    protected $AmountTo = null;

    /**
     * @var Notes $Notes
     */
    protected $Notes = null;

    /**
     * @param string $PassengerType
     * @param string $CurrencyCode
     */
    public function __construct($PassengerType, $CurrencyCode)
    {
      $this->PassengerType = $PassengerType;
      $this->CurrencyCode = $CurrencyCode;
    }

    /**
     * @return string
     */
    public function getPassengerType()
    {
      return $this->PassengerType;
    }

    /**
     * @param string $PassengerType
     * @return \ContentHub\Price
     */
    public function setPassengerType($PassengerType)
    {
      $this->PassengerType = $PassengerType;
      return $this;
    }

    /**
     * @return string
     */
    public function getCurrencyCode()
    {
      return $this->CurrencyCode;
    }

    /**
     * @param string $CurrencyCode
     * @return \ContentHub\Price
     */
    public function setCurrencyCode($CurrencyCode)
    {
      $this->CurrencyCode = $CurrencyCode;
      return $this;
    }

    /**
     * @return float
     */
    public function getAmountFrom()
    {
      return $this->AmountFrom;
    }

    /**
     * @param float $AmountFrom
     * @return \ContentHub\Price
     */
    public function setAmountFrom($AmountFrom)
    {
      $this->AmountFrom = $AmountFrom;
      return $this;
    }

    /**
     * @return float
     */
    public function getAmountTo()
    {
      return $this->AmountTo;
    }

    /**
     * @param float $AmountTo
     * @return \ContentHub\Price
     */
    public function setAmountTo($AmountTo)
    {
      $this->AmountTo = $AmountTo;
      return $this;
    }

    /**
     * @return Notes
     */
    public function getNotes()
    {
      return $this->Notes;
    }

    /**
     * @param Notes $Notes
     * @return \ContentHub\Price
     */
    public function setNotes($Notes)
    {
      $this->Notes = $Notes;
      return $this;
    }

}
