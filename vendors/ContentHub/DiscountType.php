<?php

namespace ContentHub;

class DiscountType
{

    /**
     * @var string $Code
     */
    protected $Code = null;

    /**
     * @var string $Type
     */
    protected $Type = null;

    /**
     * @var string $Basis
     */
    protected $Basis = null;

    /**
     * @var float $Amount
     */
    protected $Amount = null;

    /**
     * @var boolean $IsPercentage
     */
    protected $IsPercentage = null;

    /**
     * @var \DateTime $PaymentDueDate
     */
    protected $PaymentDueDate = null;

    /**
     * @param string $Code
     * @param string $Type
     * @param string $Basis
     * @param float $Amount
     * @param boolean $IsPercentage
     */
    public function __construct($Code, $Type, $Basis, $Amount, $IsPercentage)
    {
      $this->Code = $Code;
      $this->Type = $Type;
      $this->Basis = $Basis;
      $this->Amount = $Amount;
      $this->IsPercentage = $IsPercentage;
    }

    /**
     * @return string
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param string $Code
     * @return \ContentHub\DiscountType
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
      return $this->Type;
    }

    /**
     * @param string $Type
     * @return \ContentHub\DiscountType
     */
    public function setType($Type)
    {
      $this->Type = $Type;
      return $this;
    }

    /**
     * @return string
     */
    public function getBasis()
    {
      return $this->Basis;
    }

    /**
     * @param string $Basis
     * @return \ContentHub\DiscountType
     */
    public function setBasis($Basis)
    {
      $this->Basis = $Basis;
      return $this;
    }

    /**
     * @return float
     */
    public function getAmount()
    {
      return $this->Amount;
    }

    /**
     * @param float $Amount
     * @return \ContentHub\DiscountType
     */
    public function setAmount($Amount)
    {
      $this->Amount = $Amount;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getIsPercentage()
    {
      return $this->IsPercentage;
    }

    /**
     * @param boolean $IsPercentage
     * @return \ContentHub\DiscountType
     */
    public function setIsPercentage($IsPercentage)
    {
      $this->IsPercentage = $IsPercentage;
      return $this;
    }

    /**
     * @return \DateTime
     */
    public function getPaymentDueDate()
    {
      if ($this->PaymentDueDate == null) {
        return null;
      } else {
        try {
          return new \DateTime($this->PaymentDueDate);
        } catch (\Exception $e) {
          return false;
        }
      }
    }

    /**
     * @param \DateTime $PaymentDueDate
     * @return \ContentHub\DiscountType
     */
    public function setPaymentDueDate(\DateTime $PaymentDueDate = null)
    {
      if ($PaymentDueDate == null) {
       $this->PaymentDueDate = null;
      } else {
        $this->PaymentDueDate = $PaymentDueDate->format(\DateTime::ATOM);
      }
      return $this;
    }

}
