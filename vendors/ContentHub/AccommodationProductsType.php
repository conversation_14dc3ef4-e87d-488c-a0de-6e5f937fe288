<?php

namespace ContentHub;

class AccommodationProductsType
{

    /**
     * @var AccommodationProductType[] $AccommodationProduct
     */
    protected $AccommodationProduct = null;

    /**
     * @param AccommodationProductType[] $AccommodationProduct
     */
    public function __construct(array $AccommodationProduct)
    {
      $this->AccommodationProduct = $AccommodationProduct;
    }

    /**
     * @return AccommodationProductType[]
     */
    public function getAccommodationProduct()
    {
      return $this->AccommodationProduct;
    }

    /**
     * @param AccommodationProductType[] $AccommodationProduct
     * @return \ContentHub\AccommodationProductsType
     */
    public function setAccommodationProduct(array $AccommodationProduct)
    {
      $this->AccommodationProduct = $AccommodationProduct;
      return $this;
    }

}
