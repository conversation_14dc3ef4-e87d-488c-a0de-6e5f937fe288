<?php

namespace ContentHub;

class DepartureType
{

    /**
     * @var string $DepartureCode
     */
    protected $DepartureCode = null;

    /**
     * @var AvailabilityStatusType $AvailabilityStatus
     */
    protected $AvailabilityStatus = null;

    /**
     * @var \DateTime $StartDateTime
     */
    protected $StartDateTime = null;

    /**
     * @var \DateTime $EndDateTime
     */
    protected $EndDateTime = null;

    /**
     * @var boolean $DateRangeIncludesTravelTime
     */
    protected $DateRangeIncludesTravelTime = null;

    /**
     * @var boolean $DefiniteDeparture
     */
    protected $DefiniteDeparture = null;

    /**
     * @var Notes $Notes
     */
    protected $Notes = null;

    /**
     * @var TourRulesType $TourRules
     */
    protected $TourRules = null;

    /**
     * @var AssociatedProductsType $AssociatedProducts
     */
    protected $AssociatedProducts = null;

    /**
     * @param string $DepartureCode
     * @param AvailabilityStatusType $AvailabilityStatus
     * @param \DateTime $StartDateTime
     * @param \DateTime $EndDateTime
     * @param boolean $DateRangeIncludesTravelTime
     * @param boolean $DefiniteDeparture
     * @param TourRulesType $TourRules
     */
    public function __construct($DepartureCode, $AvailabilityStatus, \DateTime $StartDateTime, \DateTime $EndDateTime, $DateRangeIncludesTravelTime, $DefiniteDeparture, $TourRules)
    {
      $this->DepartureCode = $DepartureCode;
      $this->AvailabilityStatus = $AvailabilityStatus;
      $this->StartDateTime = $StartDateTime->format(\DateTime::ATOM);
      $this->EndDateTime = $EndDateTime->format(\DateTime::ATOM);
      $this->DateRangeIncludesTravelTime = $DateRangeIncludesTravelTime;
      $this->DefiniteDeparture = $DefiniteDeparture;
      $this->TourRules = $TourRules;
    }

    /**
     * @return string
     */
    public function getDepartureCode()
    {
      return $this->DepartureCode;
    }

    /**
     * @param string $DepartureCode
     * @return \ContentHub\DepartureType
     */
    public function setDepartureCode($DepartureCode)
    {
      $this->DepartureCode = $DepartureCode;
      return $this;
    }

    /**
     * @return AvailabilityStatusType
     */
    public function getAvailabilityStatus()
    {
      return $this->AvailabilityStatus;
    }

    /**
     * @param AvailabilityStatusType $AvailabilityStatus
     * @return \ContentHub\DepartureType
     */
    public function setAvailabilityStatus($AvailabilityStatus)
    {
      $this->AvailabilityStatus = $AvailabilityStatus;
      return $this;
    }

    /**
     * @return \DateTime
     */
    public function getStartDateTime()
    {
      if ($this->StartDateTime == null) {
        return null;
      } else {
        try {
          return new \DateTime($this->StartDateTime);
        } catch (\Exception $e) {
          return false;
        }
      }
    }

    /**
     * @param \DateTime $StartDateTime
     * @return \ContentHub\DepartureType
     */
    public function setStartDateTime(\DateTime $StartDateTime)
    {
      $this->StartDateTime = $StartDateTime->format(\DateTime::ATOM);
      return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEndDateTime()
    {
      if ($this->EndDateTime == null) {
        return null;
      } else {
        try {
          return new \DateTime($this->EndDateTime);
        } catch (\Exception $e) {
          return false;
        }
      }
    }

    /**
     * @param \DateTime $EndDateTime
     * @return \ContentHub\DepartureType
     */
    public function setEndDateTime(\DateTime $EndDateTime)
    {
      $this->EndDateTime = $EndDateTime->format(\DateTime::ATOM);
      return $this;
    }

    /**
     * @return boolean
     */
    public function getDateRangeIncludesTravelTime()
    {
      return $this->DateRangeIncludesTravelTime;
    }

    /**
     * @param boolean $DateRangeIncludesTravelTime
     * @return \ContentHub\DepartureType
     */
    public function setDateRangeIncludesTravelTime($DateRangeIncludesTravelTime)
    {
      $this->DateRangeIncludesTravelTime = $DateRangeIncludesTravelTime;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getDefiniteDeparture()
    {
      return $this->DefiniteDeparture;
    }

    /**
     * @param boolean $DefiniteDeparture
     * @return \ContentHub\DepartureType
     */
    public function setDefiniteDeparture($DefiniteDeparture)
    {
      $this->DefiniteDeparture = $DefiniteDeparture;
      return $this;
    }

    /**
     * @return Notes
     */
    public function getNotes()
    {
      return $this->Notes;
    }

    /**
     * @param Notes $Notes
     * @return \ContentHub\DepartureType
     */
    public function setNotes($Notes)
    {
      $this->Notes = $Notes;
      return $this;
    }

    /**
     * @return TourRulesType
     */
    public function getTourRules()
    {
      return $this->TourRules;
    }

    /**
     * @param TourRulesType $TourRules
     * @return \ContentHub\DepartureType
     */
    public function setTourRules($TourRules)
    {
      $this->TourRules = $TourRules;
      return $this;
    }

    /**
     * @return AssociatedProductsType
     */
    public function getAssociatedProducts()
    {
      return $this->AssociatedProducts;
    }

    /**
     * @param AssociatedProductsType $AssociatedProducts
     * @return \ContentHub\DepartureType
     */
    public function setAssociatedProducts($AssociatedProducts)
    {
      $this->AssociatedProducts = $AssociatedProducts;
      return $this;
    }

}
