<?php

namespace ContentHub;

class SurchargesType
{

    /**
     * @var SurchargeType[] $Surcharge
     */
    protected $Surcharge = null;

    /**
     * @param SurchargeType[] $Surcharge
     */
    public function __construct(array $Surcharge)
    {
      $this->Surcharge = $Surcharge;
    }

    /**
     * @return SurchargeType[]
     */
    public function getSurcharge()
    {
      return $this->Surcharge;
    }

    /**
     * @param SurchargeType[] $Surcharge
     * @return \ContentHub\SurchargesType
     */
    public function setSurcharge(array $Surcharge)
    {
      $this->Surcharge = $Surcharge;
      return $this;
    }

}
