<?php

namespace ContentHub;

class TourCategory
{

    /**
     * @var string[] $CategoryValue
     */
    protected $CategoryValue = null;

    /**
     * @var NMTOKEN $Name
     */
    protected $Name = null;

    /**
     * @param NMTOKEN $Name
     */
    public function __construct($Name)
    {
      $this->Name = $Name;
    }

    /**
     * @return string[]
     */
    public function getCategoryValue()
    {
      return $this->CategoryValue;
    }

    /**
     * @param string[] $CategoryValue
     * @return \ContentHub\TourCategory
     */
    public function setCategoryValue(array $CategoryValue = null)
    {
      $this->CategoryValue = $CategoryValue;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param NMTOKEN $Name
     * @return \ContentHub\TourCategory
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

}
