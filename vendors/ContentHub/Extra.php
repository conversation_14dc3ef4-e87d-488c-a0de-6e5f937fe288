<?php

namespace ContentHub;

class Extra
{

    /**
     * @var string $Code
     */
    protected $Code = null;

    /**
     * @var Price[] $Price
     */
    protected $Price = null;

    /**
     * @var Section[] $Section
     */
    protected $Section = null;

    /**
     * @param string $Code
     * @param Price[] $Price
     * @param Section[] $Section
     */
    public function __construct($Code, array $Price, array $Section)
    {
      $this->Code = $Code;
      $this->Price = $Price;
      $this->Section = $Section;
    }

    /**
     * @return string
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param string $Code
     * @return \ContentHub\Extra
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return Price[]
     */
    public function getPrice()
    {
      return $this->Price;
    }

    /**
     * @param Price[] $Price
     * @return \ContentHub\Extra
     */
    public function setPrice(array $Price)
    {
      $this->Price = $Price;
      return $this;
    }

    /**
     * @return Section[]
     */
    public function getSection()
    {
      return $this->Section;
    }

    /**
     * @param Section[] $Section
     * @return \ContentHub\Extra
     */
    public function setSection(array $Section)
    {
      $this->Section = $Section;
      return $this;
    }

}
