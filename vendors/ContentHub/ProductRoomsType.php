<?php

namespace ContentHub;

class ProductRoomsType
{

    /**
     * @var ProductRoomType[] $Room
     */
    protected $Room = null;

    /**
     * @param ProductRoomType[] $Room
     */
    public function __construct(array $Room)
    {
      $this->Room = $Room;
    }

    /**
     * @return ProductRoomType[]
     */
    public function getRoom()
    {
      return $this->Room;
    }

    /**
     * @param ProductRoomType[] $Room
     * @return \ContentHub\ProductRoomsType
     */
    public function setRoom(array $Room)
    {
      $this->Room = $Room;
      return $this;
    }

}
