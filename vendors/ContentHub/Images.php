<?php

namespace ContentHub;

class Images
{

    /**
     * @var Image[] $Image
     */
    protected $Image = null;

    /**
     * @param Image[] $Image
     */
    public function __construct(array $Image)
    {
      $this->Image = $Image;
    }

    /**
     * @return Image[]
     */
    public function getImage()
    {
      return $this->Image;
    }

    /**
     * @param Image[] $Image
     * @return \ContentHub\Images
     */
    public function setImage(array $Image)
    {
      $this->Image = $Image;
      return $this;
    }

}
