<?php

namespace ContentHub;

class SoapResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\SoapResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

}
