<?php

namespace ContentHub;

class TourDeparturesType
{

    /**
     * @var string $OperatingProductCode
     */
    protected $OperatingProductCode = null;

    /**
     * @var SellingCompaniesType $SellingCompanies
     */
    protected $SellingCompanies = null;

    /**
     * @var string $TourCode
     */
    protected $TourCode = null;

    /**
     * @var boolean $OnlineBookable
     */
    protected $OnlineBookable = null;

    /**
     * @param string $OperatingProductCode
     * @param SellingCompaniesType $SellingCompanies
     * @param string $TourCode
     * @param boolean $OnlineBookable
     */
    public function __construct($OperatingProductCode, $SellingCompanies, $TourCode, $OnlineBookable)
    {
      $this->OperatingProductCode = $OperatingProductCode;
      $this->SellingCompanies = $SellingCompanies;
      $this->TourCode = $TourCode;
      $this->OnlineBookable = $OnlineBookable;
    }

    /**
     * @return string
     */
    public function getOperatingProductCode()
    {
      return $this->OperatingProductCode;
    }

    /**
     * @param string $OperatingProductCode
     * @return \ContentHub\TourDeparturesType
     */
    public function setOperatingProductCode($OperatingProductCode)
    {
      $this->OperatingProductCode = $OperatingProductCode;
      return $this;
    }

    /**
     * @return SellingCompaniesType
     */
    public function getSellingCompanies()
    {
      return $this->SellingCompanies;
    }

    /**
     * @param SellingCompaniesType $SellingCompanies
     * @return \ContentHub\TourDeparturesType
     */
    public function setSellingCompanies($SellingCompanies)
    {
      $this->SellingCompanies = $SellingCompanies;
      return $this;
    }

    /**
     * @return string
     */
    public function getTourCode()
    {
      return $this->TourCode;
    }

    /**
     * @param string $TourCode
     * @return \ContentHub\TourDeparturesType
     */
    public function setTourCode($TourCode)
    {
      $this->TourCode = $TourCode;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getOnlineBookable()
    {
      return $this->OnlineBookable;
    }

    /**
     * @param boolean $OnlineBookable
     * @return \ContentHub\TourDeparturesType
     */
    public function setOnlineBookable($OnlineBookable)
    {
      $this->OnlineBookable = $OnlineBookable;
      return $this;
    }

}
