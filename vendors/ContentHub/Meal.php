<?php

namespace ContentHub;

class Meal
{

    /**
     * @var string $Type
     */
    protected $Type = null;

    /**
     * @var int $Number
     */
    protected $Number = null;

    /**
     * @param string $Type
     * @param int $Number
     */
    public function __construct($Type, $Number)
    {
      $this->Type = $Type;
      $this->Number = $Number;
    }

    /**
     * @return string
     */
    public function getType()
    {
      return $this->Type;
    }

    /**
     * @param string $Type
     * @return \ContentHub\Meal
     */
    public function setType($Type)
    {
      $this->Type = $Type;
      return $this;
    }

    /**
     * @return int
     */
    public function getNumber()
    {
      return $this->Number;
    }

    /**
     * @param int $Number
     * @return \ContentHub\Meal
     */
    public function setNumber($Number)
    {
      $this->Number = $Number;
      return $this;
    }

}
