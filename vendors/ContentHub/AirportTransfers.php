<?php

namespace ContentHub;

class AirportTransfers
{

    /**
     * @var Section[] $Section
     */
    protected $Section = null;

    /**
     * @param Section[] $Section
     */
    public function __construct(array $Section)
    {
      $this->Section = $Section;
    }

    /**
     * @return Section[]
     */
    public function getSection()
    {
      return $this->Section;
    }

    /**
     * @param Section[] $Section
     * @return \ContentHub\AirportTransfers
     */
    public function setSection(array $Section)
    {
      $this->Section = $Section;
      return $this;
    }

}
