<?php

namespace ContentHub;

class MiscellaneousProductsType
{

    /**
     * @var MiscellaneousProductType[] $MiscellaneousProduct
     */
    protected $MiscellaneousProduct = null;

    /**
     * @param MiscellaneousProductType[] $MiscellaneousProduct
     */
    public function __construct(array $MiscellaneousProduct)
    {
      $this->MiscellaneousProduct = $MiscellaneousProduct;
    }

    /**
     * @return MiscellaneousProductType[]
     */
    public function getMiscellaneousProduct()
    {
      return $this->MiscellaneousProduct;
    }

    /**
     * @param MiscellaneousProductType[] $MiscellaneousProduct
     * @return \ContentHub\MiscellaneousProductsType
     */
    public function setMiscellaneousProduct(array $MiscellaneousProduct)
    {
      $this->MiscellaneousProduct = $MiscellaneousProduct;
      return $this;
    }

}
