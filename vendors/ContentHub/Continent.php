<?php

namespace ContentHub;

class Continent
{

    /**
     * @var ContinentCode $Code
     */
    protected $Code = null;

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @param ContinentCode $Code
     * @param string $Name
     */
    public function __construct($Code, $Name)
    {
      $this->Code = $Code;
      $this->Name = $Name;
    }

    /**
     * @return ContinentCode
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param ContinentCode $Code
     * @return \ContentHub\Continent
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\Continent
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

}
