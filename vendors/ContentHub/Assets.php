<?php

namespace ContentHub;

class Assets
{

    /**
     * @var Images $Images
     */
    protected $Images = null;

    /**
     * @var Videos $Videos
     */
    protected $Videos = null;

    /**
     * @param Images $Images
     */
    public function __construct($Images)
    {
      $this->Images = $Images;
    }

    /**
     * @return Images
     */
    public function getImages()
    {
      return $this->Images;
    }

    /**
     * @param Images $Images
     * @return \ContentHub\Assets
     */
    public function setImages($Images)
    {
      $this->Images = $Images;
      return $this;
    }

    /**
     * @return Videos
     */
    public function getVideos()
    {
      return $this->Videos;
    }

    /**
     * @param Videos $Videos
     * @return \ContentHub\Assets
     */
    public function setVideos($Videos)
    {
      $this->Videos = $Videos;
      return $this;
    }

}
