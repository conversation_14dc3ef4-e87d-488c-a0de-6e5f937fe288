<?php

namespace ContentHub;

class Airport
{

    /**
     * @var string $IATACode
     */
    protected $IATACode = null;

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @var string $City
     */
    protected $City = null;

    /**
     * @var string $Region
     */
    protected $Region = null;

    /**
     * @var string $Country
     */
    protected $Country = null;

    /**
     * @var boolean $DefaultForCity
     */
    protected $DefaultForCity = null;

    /**
     * @param string $IATACode
     * @param string $Name
     * @param string $City
     * @param string $Region
     * @param string $Country
     * @param boolean $DefaultForCity
     */
    public function __construct($IATACode, $Name, $City, $Region, $Country, $DefaultForCity)
    {
      $this->IATACode = $IATACode;
      $this->Name = $Name;
      $this->City = $City;
      $this->Region = $Region;
      $this->Country = $Country;
      $this->DefaultForCity = $DefaultForCity;
    }

    /**
     * @return string
     */
    public function getIATACode()
    {
      return $this->IATACode;
    }

    /**
     * @param string $IATACode
     * @return \ContentHub\Airport
     */
    public function setIATACode($IATACode)
    {
      $this->IATACode = $IATACode;
      return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\Airport
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

    /**
     * @return string
     */
    public function getCity()
    {
      return $this->City;
    }

    /**
     * @param string $City
     * @return \ContentHub\Airport
     */
    public function setCity($City)
    {
      $this->City = $City;
      return $this;
    }

    /**
     * @return string
     */
    public function getRegion()
    {
      return $this->Region;
    }

    /**
     * @param string $Region
     * @return \ContentHub\Airport
     */
    public function setRegion($Region)
    {
      $this->Region = $Region;
      return $this;
    }

    /**
     * @return string
     */
    public function getCountry()
    {
      return $this->Country;
    }

    /**
     * @param string $Country
     * @return \ContentHub\Airport
     */
    public function setCountry($Country)
    {
      $this->Country = $Country;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getDefaultForCity()
    {
      return $this->DefaultForCity;
    }

    /**
     * @param boolean $DefaultForCity
     * @return \ContentHub\Airport
     */
    public function setDefaultForCity($DefaultForCity)
    {
      $this->DefaultForCity = $DefaultForCity;
      return $this;
    }

}
