<?php

namespace ContentHub;

class Notes
{

    /**
     * @var string $text
     */
    protected $text = null;

    /**
     * @var br $br
     */
    protected $br = null;

    /**
     * @param string $text
     * @param br $br
     */
    public function __construct($text, $br)
    {
      $this->text = $text;
      $this->br = $br;
    }

    /**
     * @return string
     */
    public function getText()
    {
      return $this->text;
    }

    /**
     * @param string $text
     * @return \ContentHub\Notes
     */
    public function setText($text)
    {
      $this->text = $text;
      return $this;
    }

    /**
     * @return br
     */
    public function getBr()
    {
      return $this->br;
    }

    /**
     * @param br $br
     * @return \ContentHub\Notes
     */
    public function setBr($br)
    {
      $this->br = $br;
      return $this;
    }

}
