<?php

namespace ContentHub;

class GetContinentsAndCountriesVisitedResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    /**
     * @var boolean $successful
     */
    protected $successful = null;

    /**
     * @var ContinentAndCountries $continentsAndCountries
     */
    protected $continentsAndCountries = null;

    /**
     * @param MessageContext $messageContext
     * @param boolean $successful
     * @param ContinentAndCountries $continentsAndCountries
     */
    public function __construct($messageContext, $successful, $continentsAndCountries)
    {
      $this->messageContext = $messageContext;
      $this->successful = $successful;
      $this->continentsAndCountries = $continentsAndCountries;
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\GetContinentsAndCountriesVisitedResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getSuccessful()
    {
      return $this->successful;
    }

    /**
     * @param boolean $successful
     * @return \ContentHub\GetContinentsAndCountriesVisitedResponse
     */
    public function setSuccessful($successful)
    {
      $this->successful = $successful;
      return $this;
    }

    /**
     * @return ContinentAndCountries
     */
    public function getContinentsAndCountries()
    {
      return $this->continentsAndCountries;
    }

    /**
     * @param ContinentAndCountries $continentsAndCountries
     * @return \ContentHub\GetContinentsAndCountriesVisitedResponse
     */
    public function setContinentsAndCountries($continentsAndCountries)
    {
      $this->continentsAndCountries = $continentsAndCountries;
      return $this;
    }

}
