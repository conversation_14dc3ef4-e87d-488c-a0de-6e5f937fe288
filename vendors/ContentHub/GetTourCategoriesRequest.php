<?php

namespace ContentHub;

class GetTourCategoriesRequest
{

    /**
     * @var string $securityKey
     */
    protected $securityKey = null;

    /**
     * @var string $sellingCompany
     */
    protected $sellingCompany = null;

    /**
     * @param string $securityKey
     * @param string $sellingCompany
     */
    public function __construct($securityKey, $sellingCompany)
    {
      $this->securityKey = $securityKey;
      $this->sellingCompany = $sellingCompany;
    }

    /**
     * @return string
     */
    public function getSecurityKey()
    {
      return $this->securityKey;
    }

    /**
     * @param string $securityKey
     * @return \ContentHub\GetTourCategoriesRequest
     */
    public function setSecurityKey($securityKey)
    {
      $this->securityKey = $securityKey;
      return $this;
    }

    /**
     * @return string
     */
    public function getSellingCompany()
    {
      return $this->sellingCompany;
    }

    /**
     * @param string $sellingCompany
     * @return \ContentHub\GetTourCategoriesRequest
     */
    public function setSellingCompany($sellingCompany)
    {
      $this->sellingCompany = $sellingCompany;
      return $this;
    }

}
