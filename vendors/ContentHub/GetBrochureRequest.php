<?php

namespace ContentHub;

class GetBrochureRequest
{

    /**
     * @var string $securityKey
     */
    protected $securityKey = null;

    /**
     * @var string $title
     */
    protected $title = null;

    /**
     * @var string $brandCode
     */
    protected $brandCode = null;

    /**
     * @var string $sellingCompanyCode
     */
    protected $sellingCompanyCode = null;

    /**
     * @var string $agentText
     */
    protected $agentText = null;

    /**
     * @var string $agentImage
     */
    protected $agentImage = null;

    /**
     * @var string $tour
     */
    protected $tour = null;

    /**
     * @param string $securityKey
     * @param string $title
     * @param string $brandCode
     * @param string $sellingCompanyCode
     * @param string $agentText
     * @param string $agentImage
     * @param string $tour
     */
    public function __construct($securityKey, $title, $brandCode, $sellingCompanyCode, $agentText, $agentImage, $tour)
    {
      $this->securityKey = $securityKey;
      $this->title = $title;
      $this->brandCode = $brandCode;
      $this->sellingCompanyCode = $sellingCompanyCode;
      $this->agentText = $agentText;
      $this->agentImage = $agentImage;
      $this->tour = $tour;
    }

    /**
     * @return string
     */
    public function getSecurityKey()
    {
      return $this->securityKey;
    }

    /**
     * @param string $securityKey
     * @return \ContentHub\GetBrochureRequest
     */
    public function setSecurityKey($securityKey)
    {
      $this->securityKey = $securityKey;
      return $this;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
      return $this->title;
    }

    /**
     * @param string $title
     * @return \ContentHub\GetBrochureRequest
     */
    public function setTitle($title)
    {
      $this->title = $title;
      return $this;
    }

    /**
     * @return string
     */
    public function getBrandCode()
    {
      return $this->brandCode;
    }

    /**
     * @param string $brandCode
     * @return \ContentHub\GetBrochureRequest
     */
    public function setBrandCode($brandCode)
    {
      $this->brandCode = $brandCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getSellingCompanyCode()
    {
      return $this->sellingCompanyCode;
    }

    /**
     * @param string $sellingCompanyCode
     * @return \ContentHub\GetBrochureRequest
     */
    public function setSellingCompanyCode($sellingCompanyCode)
    {
      $this->sellingCompanyCode = $sellingCompanyCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getAgentText()
    {
      return $this->agentText;
    }

    /**
     * @param string $agentText
     * @return \ContentHub\GetBrochureRequest
     */
    public function setAgentText($agentText)
    {
      $this->agentText = $agentText;
      return $this;
    }

    /**
     * @return string
     */
    public function getAgentImage()
    {
      return $this->agentImage;
    }

    /**
     * @param string $agentImage
     * @return \ContentHub\GetBrochureRequest
     */
    public function setAgentImage($agentImage)
    {
      $this->agentImage = $agentImage;
      return $this;
    }

    /**
     * @return string
     */
    public function getTour()
    {
      return $this->tour;
    }

    /**
     * @param string $tour
     * @return \ContentHub\GetBrochureRequest
     */
    public function setTour($tour)
    {
      $this->tour = $tour;
      return $this;
    }

}
