<?php

namespace ContentHub;

class DiscountsType
{

    /**
     * @var DiscountType[] $Discount
     */
    protected $Discount = null;

    /**
     * @param DiscountType[] $Discount
     */
    public function __construct(array $Discount)
    {
      $this->Discount = $Discount;
    }

    /**
     * @return DiscountType[]
     */
    public function getDiscount()
    {
      return $this->Discount;
    }

    /**
     * @param DiscountType[] $Discount
     * @return \ContentHub\DiscountsType
     */
    public function setDiscount(array $Discount)
    {
      $this->Discount = $Discount;
      return $this;
    }

}
