<?php

namespace ContentHub;

class GetTourDetailsFullResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    /**
     * @var boolean $successful
     */
    protected $successful = null;

    /**
     * @var TourDeparturesType $TourDepartures
     */
    protected $TourDepartures = null;

    /**
     * @var TourInfo $TourInfo
     */
    protected $TourInfo = null;

    /**
     * @param MessageContext $messageContext
     * @param boolean $successful
     * @param TourDeparturesType $TourDepartures
     * @param TourInfo $TourInfo
     */
    public function __construct($messageContext, $successful, $TourDepartures, $TourInfo)
    {
      $this->messageContext = $messageContext;
      $this->successful = $successful;
      $this->TourDepartures = $TourDepartures;
      $this->TourInfo = $TourInfo;
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\GetTourDetailsFullResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getSuccessful()
    {
      return $this->successful;
    }

    /**
     * @param boolean $successful
     * @return \ContentHub\GetTourDetailsFullResponse
     */
    public function setSuccessful($successful)
    {
      $this->successful = $successful;
      return $this;
    }

    /**
     * @return TourDeparturesType
     */
    public function getTourDepartures()
    {
      return $this->TourDepartures;
    }

    /**
     * @param TourDeparturesType $TourDepartures
     * @return \ContentHub\GetTourDetailsFullResponse
     */
    public function setTourDepartures($TourDepartures)
    {
      $this->TourDepartures = $TourDepartures;
      return $this;
    }

    /**
     * @return TourInfo
     */
    public function getTourInfo()
    {
      return $this->TourInfo;
    }

    /**
     * @param TourInfo $TourInfo
     * @return \ContentHub\GetTourDetailsFullResponse
     */
    public function setTourInfo($TourInfo)
    {
      $this->TourInfo = $TourInfo;
      return $this;
    }

}
