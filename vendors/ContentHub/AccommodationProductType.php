<?php

namespace ContentHub;

class AccommodationProductType
{

    /**
     * @var string $Code
     */
    protected $Code = null;

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @var AddressType $Address
     */
    protected $Address = null;

    /**
     * @var ProductRoomsType $Rooms
     */
    protected $Rooms = null;

    /**
     * @var AccommodationProductTypeCodeType $Type
     */
    protected $Type = null;

    /**
     * @param string $Code
     * @param string $Name
     * @param AddressType $Address
     * @param ProductRoomsType $Rooms
     * @param AccommodationProductTypeCodeType $Type
     */
    public function __construct($Code, $Name, $Address, $Rooms, $Type)
    {
      $this->Code = $Code;
      $this->Name = $Name;
      $this->Address = $Address;
      $this->Rooms = $Rooms;
      $this->Type = $Type;
    }

    /**
     * @return string
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param string $Code
     * @return \ContentHub\AccommodationProductType
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\AccommodationProductType
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

    /**
     * @return AddressType
     */
    public function getAddress()
    {
      return $this->Address;
    }

    /**
     * @param AddressType $Address
     * @return \ContentHub\AccommodationProductType
     */
    public function setAddress($Address)
    {
      $this->Address = $Address;
      return $this;
    }

    /**
     * @return ProductRoomsType
     */
    public function getRooms()
    {
      return $this->Rooms;
    }

    /**
     * @param ProductRoomsType $Rooms
     * @return \ContentHub\AccommodationProductType
     */
    public function setRooms($Rooms)
    {
      $this->Rooms = $Rooms;
      return $this;
    }

    /**
     * @return AccommodationProductTypeCodeType
     */
    public function getType()
    {
      return $this->Type;
    }

    /**
     * @param AccommodationProductTypeCodeType $Type
     * @return \ContentHub\AccommodationProductType
     */
    public function setType($Type)
    {
      $this->Type = $Type;
      return $this;
    }

}
