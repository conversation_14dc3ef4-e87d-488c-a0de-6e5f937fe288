<?php

namespace ContentHub;

class LocationsVisited
{

    /**
     * @var Location[] $Location
     */
    protected $Location = null;

    /**
     * @param Location[] $Location
     */
    public function __construct(array $Location)
    {
      $this->Location = $Location;
    }

    /**
     * @return Location[]
     */
    public function getLocation()
    {
      return $this->Location;
    }

    /**
     * @param Location[] $Location
     * @return \ContentHub\LocationsVisited
     */
    public function setLocation(array $Location)
    {
      $this->Location = $Location;
      return $this;
    }

}
