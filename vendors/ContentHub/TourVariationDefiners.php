<?php

namespace ContentHub;

class TourVariationDefiners
{

    /**
     * @var OperatingProduct $OperatingProduct
     */
    protected $OperatingProduct = null;

    /**
     * @var IncludedSubProducts $IncludedSubProducts
     */
    protected $IncludedSubProducts = null;

    /**
     * @var RoomTypes $RoomTypes
     */
    protected $RoomTypes = null;

    /**
     * @var City $StartCity
     */
    protected $StartCity = null;

    /**
     * @var City $EndCity
     */
    protected $EndCity = null;

    /**
     * @var boolean $IsTourPackage
     */
    protected $IsTourPackage = null;

    /**
     * @var string $IncludedCruiseCabinType
     */
    protected $IncludedCruiseCabinType = null;

    /**
     * @var AdditionalDefiners $AdditionalDefiners
     */
    protected $AdditionalDefiners = null;

    /**
     * @param OperatingProduct $OperatingProduct
     * @param RoomTypes $RoomTypes
     * @param City $StartCity
     * @param City $EndCity
     * @param boolean $IsTourPackage
     */
    public function __construct($OperatingProduct, $RoomTypes, $StartCity, $EndCity, $IsTourPackage)
    {
      $this->OperatingProduct = $OperatingProduct;
      $this->RoomTypes = $RoomTypes;
      $this->StartCity = $StartCity;
      $this->EndCity = $EndCity;
      $this->IsTourPackage = $IsTourPackage;
    }

    /**
     * @return OperatingProduct
     */
    public function getOperatingProduct()
    {
      return $this->OperatingProduct;
    }

    /**
     * @param OperatingProduct $OperatingProduct
     * @return \ContentHub\TourVariationDefiners
     */
    public function setOperatingProduct($OperatingProduct)
    {
      $this->OperatingProduct = $OperatingProduct;
      return $this;
    }

    /**
     * @return IncludedSubProducts
     */
    public function getIncludedSubProducts()
    {
      return $this->IncludedSubProducts;
    }

    /**
     * @param IncludedSubProducts $IncludedSubProducts
     * @return \ContentHub\TourVariationDefiners
     */
    public function setIncludedSubProducts($IncludedSubProducts)
    {
      $this->IncludedSubProducts = $IncludedSubProducts;
      return $this;
    }

    /**
     * @return RoomTypes
     */
    public function getRoomTypes()
    {
      return $this->RoomTypes;
    }

    /**
     * @param RoomTypes $RoomTypes
     * @return \ContentHub\TourVariationDefiners
     */
    public function setRoomTypes($RoomTypes)
    {
      $this->RoomTypes = $RoomTypes;
      return $this;
    }

    /**
     * @return City
     */
    public function getStartCity()
    {
      return $this->StartCity;
    }

    /**
     * @param City $StartCity
     * @return \ContentHub\TourVariationDefiners
     */
    public function setStartCity($StartCity)
    {
      $this->StartCity = $StartCity;
      return $this;
    }

    /**
     * @return City
     */
    public function getEndCity()
    {
      return $this->EndCity;
    }

    /**
     * @param City $EndCity
     * @return \ContentHub\TourVariationDefiners
     */
    public function setEndCity($EndCity)
    {
      $this->EndCity = $EndCity;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getIsTourPackage()
    {
      return $this->IsTourPackage;
    }

    /**
     * @param boolean $IsTourPackage
     * @return \ContentHub\TourVariationDefiners
     */
    public function setIsTourPackage($IsTourPackage)
    {
      $this->IsTourPackage = $IsTourPackage;
      return $this;
    }

    /**
     * @return string
     */
    public function getIncludedCruiseCabinType()
    {
      return $this->IncludedCruiseCabinType;
    }

    /**
     * @param string $IncludedCruiseCabinType
     * @return \ContentHub\TourVariationDefiners
     */
    public function setIncludedCruiseCabinType($IncludedCruiseCabinType)
    {
      $this->IncludedCruiseCabinType = $IncludedCruiseCabinType;
      return $this;
    }

    /**
     * @return AdditionalDefiners
     */
    public function getAdditionalDefiners()
    {
      return $this->AdditionalDefiners;
    }

    /**
     * @param AdditionalDefiners $AdditionalDefiners
     * @return \ContentHub\TourVariationDefiners
     */
    public function setAdditionalDefiners($AdditionalDefiners)
    {
      $this->AdditionalDefiners = $AdditionalDefiners;
      return $this;
    }

}
