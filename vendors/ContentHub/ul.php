<?php

namespace ContentHub;

class ul
{

    /**
     * @var li[] $li
     */
    protected $li = null;

    /**
     * @param li[] $li
     */
    public function __construct(array $li)
    {
      $this->li = $li;
    }

    /**
     * @return li[]
     */
    public function getLi()
    {
      return $this->li;
    }

    /**
     * @param li[] $li
     * @return \ContentHub\ul
     */
    public function setLi(array $li)
    {
      $this->li = $li;
      return $this;
    }

}
