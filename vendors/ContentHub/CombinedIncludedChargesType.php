<?php

namespace ContentHub;

class CombinedIncludedChargesType
{

    /**
     * @var FoodFundType $FoodFund
     */
    protected $FoodFund = null;

    /**
     * @var MandatoryMiscellaneousProductsType $MandatoryMiscellaneousProducts
     */
    protected $MandatoryMiscellaneousProducts = null;

    /**
     * @var PortTaxType $PortTax
     */
    protected $PortTax = null;

    /**
     * @var SurchargesType $Surcharges
     */
    protected $Surcharges = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return FoodFundType
     */
    public function getFoodFund()
    {
      return $this->FoodFund;
    }

    /**
     * @param FoodFundType $FoodFund
     * @return \ContentHub\CombinedIncludedChargesType
     */
    public function setFoodFund($FoodFund)
    {
      $this->FoodFund = $FoodFund;
      return $this;
    }

    /**
     * @return MandatoryMiscellaneousProductsType
     */
    public function getMandatoryMiscellaneousProducts()
    {
      return $this->MandatoryMiscellaneousProducts;
    }

    /**
     * @param MandatoryMiscellaneousProductsType $MandatoryMiscellaneousProducts
     * @return \ContentHub\CombinedIncludedChargesType
     */
    public function setMandatoryMiscellaneousProducts($MandatoryMiscellaneousProducts)
    {
      $this->MandatoryMiscellaneousProducts = $MandatoryMiscellaneousProducts;
      return $this;
    }

    /**
     * @return PortTaxType
     */
    public function getPortTax()
    {
      return $this->PortTax;
    }

    /**
     * @param PortTaxType $PortTax
     * @return \ContentHub\CombinedIncludedChargesType
     */
    public function setPortTax($PortTax)
    {
      $this->PortTax = $PortTax;
      return $this;
    }

    /**
     * @return SurchargesType
     */
    public function getSurcharges()
    {
      return $this->Surcharges;
    }

    /**
     * @param SurchargesType $Surcharges
     * @return \ContentHub\CombinedIncludedChargesType
     */
    public function setSurcharges($Surcharges)
    {
      $this->Surcharges = $Surcharges;
      return $this;
    }

}
