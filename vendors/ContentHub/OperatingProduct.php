<?php

namespace ContentHub;

class OperatingProduct
{

    /**
     * @var string $Code
     */
    protected $Code = null;

    /**
     * @var string $ContractingSeason
     */
    protected $ContractingSeason = null;

    /**
     * @var string $Category
     */
    protected $Category = null;

    /**
     * @var string $Classification
     */
    protected $Classification = null;

    /**
     * @var string $StandardName
     */
    protected $StandardName = null;

    /**
     * @param string $Code
     * @param string $ContractingSeason
     * @param string $Category
     * @param string $Classification
     * @param string $StandardName
     */
    public function __construct($Code, $ContractingSeason, $Category, $Classification, $StandardName)
    {
      $this->Code = $Code;
      $this->ContractingSeason = $ContractingSeason;
      $this->Category = $Category;
      $this->Classification = $Classification;
      $this->StandardName = $StandardName;
    }

    /**
     * @return string
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param string $Code
     * @return \ContentHub\OperatingProduct
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getContractingSeason()
    {
      return $this->ContractingSeason;
    }

    /**
     * @param string $ContractingSeason
     * @return \ContentHub\OperatingProduct
     */
    public function setContractingSeason($ContractingSeason)
    {
      $this->ContractingSeason = $ContractingSeason;
      return $this;
    }

    /**
     * @return string
     */
    public function getCategory()
    {
      return $this->Category;
    }

    /**
     * @param string $Category
     * @return \ContentHub\OperatingProduct
     */
    public function setCategory($Category)
    {
      $this->Category = $Category;
      return $this;
    }

    /**
     * @return string
     */
    public function getClassification()
    {
      return $this->Classification;
    }

    /**
     * @param string $Classification
     * @return \ContentHub\OperatingProduct
     */
    public function setClassification($Classification)
    {
      $this->Classification = $Classification;
      return $this;
    }

    /**
     * @return string
     */
    public function getStandardName()
    {
      return $this->StandardName;
    }

    /**
     * @param string $StandardName
     * @return \ContentHub\OperatingProduct
     */
    public function setStandardName($StandardName)
    {
      $this->StandardName = $StandardName;
      return $this;
    }

}
