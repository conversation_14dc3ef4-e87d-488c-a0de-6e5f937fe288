<?php

namespace ContentHub;

class PassengersType
{

    /**
     * @var PassengerType[] $Passenger
     */
    protected $Passenger = null;

    /**
     * @param PassengerType[] $Passenger
     */
    public function __construct(array $Passenger)
    {
      $this->Passenger = $Passenger;
    }

    /**
     * @return PassengerType[]
     */
    public function getPassenger()
    {
      return $this->Passenger;
    }

    /**
     * @param PassengerType[] $Passenger
     * @return \ContentHub\PassengersType
     */
    public function setPassenger(array $Passenger)
    {
      $this->Passenger = $Passenger;
      return $this;
    }

}
