<?php

namespace ContentHub;

class MarketingFlags
{

    /**
     * @var string $MarketingPriority
     */
    protected $MarketingPriority = null;

    /**
     * @var boolean $MostPopular
     */
    protected $MostPopular = null;

    /**
     * @var KeywordsPhrases $KeywordsPhrases
     */
    protected $KeywordsPhrases = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getMarketingPriority()
    {
      return $this->MarketingPriority;
    }

    /**
     * @param string $MarketingPriority
     * @return \ContentHub\MarketingFlags
     */
    public function setMarketingPriority($MarketingPriority)
    {
      $this->MarketingPriority = $MarketingPriority;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getMostPopular()
    {
      return $this->MostPopular;
    }

    /**
     * @param boolean $MostPopular
     * @return \ContentHub\MarketingFlags
     */
    public function setMostPopular($MostPopular)
    {
      $this->MostPopular = $MostPopular;
      return $this;
    }

    /**
     * @return KeywordsPhrases
     */
    public function getKeywordsPhrases()
    {
      return $this->KeywordsPhrases;
    }

    /**
     * @param KeywordsPhrases $KeywordsPhrases
     * @return \ContentHub\MarketingFlags
     */
    public function setKeywordsPhrases($KeywordsPhrases)
    {
      $this->KeywordsPhrases = $KeywordsPhrases;
      return $this;
    }

}
