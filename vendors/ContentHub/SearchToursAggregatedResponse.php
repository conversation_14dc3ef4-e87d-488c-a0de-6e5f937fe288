<?php

namespace ContentHub;

class SearchToursAggregatedResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    /**
     * @var boolean $successful
     */
    protected $successful = null;

    /**
     * @var int $totalRecords
     */
    protected $totalRecords = null;

    /**
     * @var int $numberOfRecords
     */
    protected $numberOfRecords = null;

    /**
     * @var string $subsetReturned
     */
    protected $subsetReturned = null;

    /**
     * @var SearchAggregatedResults $searchAggregatedResults
     */
    protected $searchAggregatedResults = null;

    /**
     * @param MessageContext $messageContext
     * @param boolean $successful
     * @param int $totalRecords
     * @param int $numberOfRecords
     * @param string $subsetReturned
     * @param SearchAggregatedResults $searchAggregatedResults
     */
    public function __construct($messageContext, $successful, $totalRecords, $numberOfRecords, $subsetReturned, $searchAggregatedResults)
    {
      $this->messageContext = $messageContext;
      $this->successful = $successful;
      $this->totalRecords = $totalRecords;
      $this->numberOfRecords = $numberOfRecords;
      $this->subsetReturned = $subsetReturned;
      $this->searchAggregatedResults = $searchAggregatedResults;
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\SearchToursAggregatedResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getSuccessful()
    {
      return $this->successful;
    }

    /**
     * @param boolean $successful
     * @return \ContentHub\SearchToursAggregatedResponse
     */
    public function setSuccessful($successful)
    {
      $this->successful = $successful;
      return $this;
    }

    /**
     * @return int
     */
    public function getTotalRecords()
    {
      return $this->totalRecords;
    }

    /**
     * @param int $totalRecords
     * @return \ContentHub\SearchToursAggregatedResponse
     */
    public function setTotalRecords($totalRecords)
    {
      $this->totalRecords = $totalRecords;
      return $this;
    }

    /**
     * @return int
     */
    public function getNumberOfRecords()
    {
      return $this->numberOfRecords;
    }

    /**
     * @param int $numberOfRecords
     * @return \ContentHub\SearchToursAggregatedResponse
     */
    public function setNumberOfRecords($numberOfRecords)
    {
      $this->numberOfRecords = $numberOfRecords;
      return $this;
    }

    /**
     * @return string
     */
    public function getSubsetReturned()
    {
      return $this->subsetReturned;
    }

    /**
     * @param string $subsetReturned
     * @return \ContentHub\SearchToursAggregatedResponse
     */
    public function setSubsetReturned($subsetReturned)
    {
      $this->subsetReturned = $subsetReturned;
      return $this;
    }

    /**
     * @return SearchAggregatedResults
     */
    public function getSearchAggregatedResults()
    {
      return $this->searchAggregatedResults;
    }

    /**
     * @param SearchAggregatedResults $searchAggregatedResults
     * @return \ContentHub\SearchToursAggregatedResponse
     */
    public function setSearchAggregatedResults($searchAggregatedResults)
    {
      $this->searchAggregatedResults = $searchAggregatedResults;
      return $this;
    }

}
