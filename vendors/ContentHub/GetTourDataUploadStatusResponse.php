<?php

namespace ContentHub;

class GetTourDataUploadStatusResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    /**
     * @var boolean $successful
     */
    protected $successful = null;

    /**
     * @var CurrentBrandStatusType $currentBrandStatus
     */
    protected $currentBrandStatus = null;

    /**
     * @var UploadFileStatusType $uploadFileStatus
     */
    protected $uploadFileStatus = null;

    /**
     * @param MessageContext $messageContext
     * @param boolean $successful
     * @param CurrentBrandStatusType $currentBrandStatus
     * @param UploadFileStatusType $uploadFileStatus
     */
    public function __construct($messageContext, $successful, $currentBrandStatus, $uploadFileStatus)
    {
      $this->messageContext = $messageContext;
      $this->successful = $successful;
      $this->currentBrandStatus = $currentBrandStatus;
      $this->uploadFileStatus = $uploadFileStatus;
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\GetTourDataUploadStatusResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getSuccessful()
    {
      return $this->successful;
    }

    /**
     * @param boolean $successful
     * @return \ContentHub\GetTourDataUploadStatusResponse
     */
    public function setSuccessful($successful)
    {
      $this->successful = $successful;
      return $this;
    }

    /**
     * @return CurrentBrandStatusType
     */
    public function getCurrentBrandStatus()
    {
      return $this->currentBrandStatus;
    }

    /**
     * @param CurrentBrandStatusType $currentBrandStatus
     * @return \ContentHub\GetTourDataUploadStatusResponse
     */
    public function setCurrentBrandStatus($currentBrandStatus)
    {
      $this->currentBrandStatus = $currentBrandStatus;
      return $this;
    }

    /**
     * @return UploadFileStatusType
     */
    public function getUploadFileStatus()
    {
      return $this->uploadFileStatus;
    }

    /**
     * @param UploadFileStatusType $uploadFileStatus
     * @return \ContentHub\GetTourDataUploadStatusResponse
     */
    public function setUploadFileStatus($uploadFileStatus)
    {
      $this->uploadFileStatus = $uploadFileStatus;
      return $this;
    }

}
