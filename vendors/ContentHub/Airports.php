<?php

namespace ContentHub;

class Airports
{

    /**
     * @var Airport[] $Airport
     */
    protected $Airport = null;

    /**
     * @param Airport[] $Airport
     */
    public function __construct(array $Airport)
    {
      $this->Airport = $Airport;
    }

    /**
     * @return Airport[]
     */
    public function getAirport()
    {
      return $this->Airport;
    }

    /**
     * @param Airport[] $Airport
     * @return \ContentHub\Airports
     */
    public function setAirport(array $Airport)
    {
      $this->Airport = $Airport;
      return $this;
    }

}
