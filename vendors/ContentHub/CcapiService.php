<?php

namespace ContentHub;

class CcapiService extends \BeSimple\SoapClient\SoapClient
{

    /**
     * @var array $classmap The defined classes
     */
    private static $classmap = array (
      'UploadTourInfoRequest' => 'ContentHub\\UploadTourInfoRequest',
      'UploadTourInfoResponse' => 'ContentHub\\UploadTourInfoResponse',
      'Message' => 'ContentHub\\Message',
      'MessageType' => 'ContentHub\\MessageType',
      'MessageSubject' => 'ContentHub\\MessageSubject',
      'SoapResponse' => 'ContentHub\\SoapResponse',
      'MessageContext' => 'ContentHub\\MessageContext',
      'GetBrochureRequest' => 'ContentHub\\GetBrochureRequest',
      'GetBrochureResponse' => 'ContentHub\\GetBrochureResponse',
      'GetContinentsAndCountriesVisitedRequest' => 'ContentHub\\GetContinentsAndCountriesVisitedRequest',
      'GetContinentsAndCountriesVisitedResponse' => 'ContentHub\\GetContinentsAndCountriesVisitedResponse',
      'ContinentAndCountries' => 'ContentHub\\ContinentAndCountries',
      'GetTourCategoriesRequest' => 'ContentHub\\GetTourCategoriesRequest',
      'GetTourCategoriesResponse' => 'ContentHub\\GetTourCategoriesResponse',
      'TourCategories' => 'ContentHub\\TourCategories',
      'SearchToursRequest' => 'ContentHub\\SearchToursRequest',
      'SearchToursResponse' => 'ContentHub\\SearchToursResponse',
      'SearchResults' => 'ContentHub\\SearchResults',
      'SearchToursBaseRequest' => 'ContentHub\\SearchToursBaseRequest',
      'TourInfo' => 'ContentHub\\TourInfo',
      'CataloguedTour' => 'ContentHub\\CataloguedTour',
      'SellingCompanies' => 'ContentHub\\SellingCompanies',
      'SellingCompany' => 'ContentHub\\SellingCompany',
      'Brochure' => 'ContentHub\\Brochure',
      'TourCategory' => 'ContentHub\\TourCategory',
      'MarketingFlags' => 'ContentHub\\MarketingFlags',
      'KeywordsPhrases' => 'ContentHub\\KeywordsPhrases',
      'Text' => 'ContentHub\\Text',
      'a' => 'ContentHub\\a',
      'strong' => 'ContentHub\\strong',
      'br' => 'ContentHub\\br',
      'ul' => 'ContentHub\\ul',
      'li' => 'ContentHub\\li',
      'TourVariationDefiners' => 'ContentHub\\TourVariationDefiners',
      'OperatingProduct' => 'ContentHub\\OperatingProduct',
      'IncludedSubProducts' => 'ContentHub\\IncludedSubProducts',
      'IncludedSubProduct' => 'ContentHub\\IncludedSubProduct',
      'RoomTypes' => 'ContentHub\\RoomTypes',
      'RoomType' => 'ContentHub\\RoomType',
      'City' => 'ContentHub\\City',
      'Airports' => 'ContentHub\\Airports',
      'Airport' => 'ContentHub\\Airport',
      'ContinentsVisited' => 'ContentHub\\ContinentsVisited',
      'Continent' => 'ContentHub\\Continent',
      'CountriesVisited' => 'ContentHub\\CountriesVisited',
      'Country' => 'ContentHub\\Country',
      'LocationsVisited' => 'ContentHub\\LocationsVisited',
      'Location' => 'ContentHub\\Location',
      'Assets' => 'ContentHub\\Assets',
      'Images' => 'ContentHub\\Images',
      'Image' => 'ContentHub\\Image',
      'Videos' => 'ContentHub\\Videos',
      'Video' => 'ContentHub\\Video',
      'Itinerary' => 'ContentHub\\Itinerary',
      'ItinerarySegment' => 'ContentHub\\ItinerarySegment',
      'Meals' => 'ContentHub\\Meals',
      'Meal' => 'ContentHub\\Meal',
      'OptionalExtras' => 'ContentHub\\OptionalExtras',
      'Extra' => 'ContentHub\\Extra',
      'Price' => 'ContentHub\\Price',
      'Section' => 'ContentHub\\Section',
      'WhatsIncluded' => 'ContentHub\\WhatsIncluded',
      'Highlights' => 'ContentHub\\Highlights',
      'AirportTransfers' => 'ContentHub\\AirportTransfers',
      'Notes' => 'ContentHub\\Notes',
      'AdditionalDefiners' => 'ContentHub\\AdditionalDefiners',
      'AdditionalInfo' => 'ContentHub\\AdditionalInfo',
      'PriceType' => 'ContentHub\\PriceType',
      'RoomPriceType' => 'ContentHub\\RoomPriceType',
      'Adult' => 'ContentHub\\Adult',
      'Child' => 'ContentHub\\Child',
      'FoodFundType' => 'ContentHub\\FoodFundType',
      'MiscellaneousProductType' => 'ContentHub\\MiscellaneousProductType',
      'MandatoryMiscellaneousProductsType' => 'ContentHub\\MandatoryMiscellaneousProductsType',
      'PortTaxType' => 'ContentHub\\PortTaxType',
      'SurchargeType' => 'ContentHub\\SurchargeType',
      'SurchargesType' => 'ContentHub\\SurchargesType',
      'CombinedIncludedChargesType' => 'ContentHub\\CombinedIncludedChargesType',
      'PassengerType' => 'ContentHub\\PassengerType',
      'PassengersType' => 'ContentHub\\PassengersType',
      'OccupancyRuleType' => 'ContentHub\\OccupancyRuleType',
      'RoomsType' => 'ContentHub\\RoomsType',
      'ProductRoomType' => 'ContentHub\\ProductRoomType',
      'ProductRoomsType' => 'ContentHub\\ProductRoomsType',
      'DiscountType' => 'ContentHub\\DiscountType',
      'DiscountsType' => 'ContentHub\\DiscountsType',
      'TourRulesType' => 'ContentHub\\TourRulesType',
      'AddressType' => 'ContentHub\\AddressType',
      'AccommodationProductType' => 'ContentHub\\AccommodationProductType',
      'AccommodationProductsType' => 'ContentHub\\AccommodationProductsType',
      'MiscellaneousProductsType' => 'ContentHub\\MiscellaneousProductsType',
      'AssociatedProductsType' => 'ContentHub\\AssociatedProductsType',
      'DepartureType' => 'ContentHub\\DepartureType',
      'DeparturesType' => 'ContentHub\\DeparturesType',
      'SellingCompanyType' => 'ContentHub\\SellingCompanyType',
      'SellingCompaniesType' => 'ContentHub\\SellingCompaniesType',
      'TourDeparturesType' => 'ContentHub\\TourDeparturesType',
      'SearchToursAggregatedRequest' => 'ContentHub\\SearchToursAggregatedRequest',
      'SearchToursAggregatedResponse' => 'ContentHub\\SearchToursAggregatedResponse',
      'SearchAggregatedResults' => 'ContentHub\\SearchAggregatedResults',
      'SearchAggregatedSubResults' => 'ContentHub\\SearchAggregatedSubResults',
      'GetTourDetailsFullRequest' => 'ContentHub\\GetTourDetailsFullRequest',
      'GetTourDetailsFullResponse' => 'ContentHub\\GetTourDetailsFullResponse',
      'GetTourDataUploadStatusRequest' => 'ContentHub\\GetTourDataUploadStatusRequest',
      'GetTourDataUploadStatusResponse' => 'ContentHub\\GetTourDataUploadStatusResponse',
      'CurrentBrandStatusType' => 'ContentHub\\CurrentBrandStatusType',
      'SnapshotType' => 'ContentHub\\SnapshotType',
      'UploadFileStatusType' => 'ContentHub\\UploadFileStatusType',
    );

    /**
     * @param array $options A array of config values
     * @param string $wsdl The wsdl file to use
     */
    public function __construct(array $options = array(), $wsdl = 'https://content.travcorp.com/ccapi/v3/CCAPIv3.wsdl')
    {
      foreach (self::$classmap as $key => $value) {
        if (!isset($options['classmap'][$key])) {
          $options['classmap'][$key] = $value;
        }
      }
      $options = array_merge(array (
      'trace' => 'true',
      'features' => SOAP_SINGLE_ELEMENT_ARRAYS,
    ), $options);
      parent::__construct($wsdl, $options);
    }

    /**
     * @param UploadTourInfoRequest $UploadTourInfoRequest
     * @return UploadTourInfoResponse
     */
    public function UploadTourInfo(UploadTourInfoRequest $UploadTourInfoRequest)
    {
      return $this->__soapCall('UploadTourInfo', array($UploadTourInfoRequest));
    }

    /**
     * @param GetTourCategoriesRequest $GetTourCategoriesRequest
     * @return GetTourCategoriesResponse
     */
    public function GetTourCategories(GetTourCategoriesRequest $GetTourCategoriesRequest)
    {
      return $this->__soapCall('GetTourCategories', array($GetTourCategoriesRequest));
    }

    /**
     * @param GetTourDataUploadStatusRequest $GetTourDataUploadStatusRequest
     * @return GetTourDataUploadStatusResponse
     */
    public function GetTourDataUploadStatus(GetTourDataUploadStatusRequest $GetTourDataUploadStatusRequest)
    {
      return $this->__soapCall('GetTourDataUploadStatus', array($GetTourDataUploadStatusRequest));
    }

    /**
     * @param GetContinentsAndCountriesVisitedRequest $GetContinentsAndCountriesVisitedRequest
     * @return GetContinentsAndCountriesVisitedResponse
     */
    public function GetContinentsAndCountriesVisited(GetContinentsAndCountriesVisitedRequest $GetContinentsAndCountriesVisitedRequest)
    {
      return $this->__soapCall('GetContinentsAndCountriesVisited', array($GetContinentsAndCountriesVisitedRequest));
    }

    /**
     * @param SearchToursAggregatedRequest $SearchToursAggregatedRequest
     * @return SearchToursAggregatedResponse
     */
    public function SearchToursAggregated(SearchToursAggregatedRequest $SearchToursAggregatedRequest)
    {
      return $this->__soapCall('SearchToursAggregated', array($SearchToursAggregatedRequest));
    }

    /**
     * @param GetBrochureRequest $GetBrochureRequest
     * @return GetBrochureResponse
     */
    public function GetBrochure(GetBrochureRequest $GetBrochureRequest)
    {
      return $this->__soapCall('GetBrochure', array($GetBrochureRequest));
    }

    /**
     * @param SearchToursRequest $SearchToursRequest
     * @return SearchToursResponse
     */
    public function SearchTours(SearchToursRequest $SearchToursRequest)
    {
      return $this->__soapCall('SearchTours', array($SearchToursRequest));
    }

    /**
     * @param GetTourDetailsFullRequest $GetTourDetailsFullRequest
     * @return GetTourDetailsFullResponse
     */
    public function GetTourDetailsFull(GetTourDetailsFullRequest $GetTourDetailsFullRequest)
    {
      return $this->__soapCall('GetTourDetailsFull', array($GetTourDetailsFullRequest));
    }

}
