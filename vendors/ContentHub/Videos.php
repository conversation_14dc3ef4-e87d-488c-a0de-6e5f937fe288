<?php

namespace ContentHub;

class Videos
{

    /**
     * @var Video[] $Video
     */
    protected $Video = null;

    /**
     * @param Video[] $Video
     */
    public function __construct(array $Video)
    {
      $this->Video = $Video;
    }

    /**
     * @return Video[]
     */
    public function getVideo()
    {
      return $this->Video;
    }

    /**
     * @param Video[] $Video
     * @return \ContentHub\Videos
     */
    public function setVideo(array $Video)
    {
      $this->Video = $Video;
      return $this;
    }

}
