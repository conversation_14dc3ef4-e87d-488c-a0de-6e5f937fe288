<?php

namespace ContentHub;

class Adult
{

    /**
     * @var float $Base
     */
    protected $Base = null;

    /**
     * @var float $Combined
     */
    protected $Combined = null;

    /**
     * @param float $Base
     * @param float $Combined
     */
    public function __construct($Base, $Combined)
    {
      $this->Base = $Base;
      $this->Combined = $Combined;
    }

    /**
     * @return float
     */
    public function getBase()
    {
      return $this->Base;
    }

    /**
     * @param float $Base
     * @return \ContentHub\Adult
     */
    public function setBase($Base)
    {
      $this->Base = $Base;
      return $this;
    }

    /**
     * @return float
     */
    public function getCombined()
    {
      return $this->Combined;
    }

    /**
     * @param float $Combined
     * @return \ContentHub\Adult
     */
    public function setCombined($Combined)
    {
      $this->Combined = $Combined;
      return $this;
    }

}
