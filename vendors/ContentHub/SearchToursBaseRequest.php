<?php

namespace ContentHub;

class SearchToursBaseRequest
{

    /**
     * @var string $securityKey
     */
    protected $securityKey = null;

    /**
     * @var string[] $sellingCompanies
     */
    protected $sellingCompanies = null;

    /**
     * @var string $continentCodes
     */
    protected $continentCodes = null;

    /**
     * @var string $countryCodes
     */
    protected $countryCodes = null;

    /**
     * @var int $durationFrom
     */
    protected $durationFrom = null;

    /**
     * @var int $durationTo
     */
    protected $durationTo = null;

    /**
     * @var string $months
     */
    protected $months = null;

    /**
     * @var string[] $keywordsAndPhrases
     */
    protected $keywordsAndPhrases = null;

    /**
     * @var string $preferedRoomType
     */
    protected $preferedRoomType = null;

    /**
     * @var float $priceFrom
     */
    protected $priceFrom = null;

    /**
     * @var float $priceTo
     */
    protected $priceTo = null;

    /**
     * @var int $firstRecordNumber
     */
    protected $firstRecordNumber = null;

    /**
     * @var int $numberOfRecords
     */
    protected $numberOfRecords = null;

    /**
     * @var string $orderBy
     */
    protected $orderBy = null;

    /**
     * @var string $orderDirection
     */
    protected $orderDirection = null;

    /**
     * @param string $securityKey
     * @param string[] $sellingCompanies
     * @param int $firstRecordNumber
     * @param int $numberOfRecords
     */
    public function __construct($securityKey, array $sellingCompanies, $firstRecordNumber, $numberOfRecords)
    {
      $this->securityKey = $securityKey;
      $this->sellingCompanies = $sellingCompanies;
      $this->firstRecordNumber = $firstRecordNumber;
      $this->numberOfRecords = $numberOfRecords;
    }

    /**
     * @return string
     */
    public function getSecurityKey()
    {
      return $this->securityKey;
    }

    /**
     * @param string $securityKey
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setSecurityKey($securityKey)
    {
      $this->securityKey = $securityKey;
      return $this;
    }

    /**
     * @return string[]
     */
    public function getSellingCompanies()
    {
      return $this->sellingCompanies;
    }

    /**
     * @param string[] $sellingCompanies
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setSellingCompanies(array $sellingCompanies)
    {
      $this->sellingCompanies = $sellingCompanies;
      return $this;
    }

    /**
     * @return string
     */
    public function getContinentCodes()
    {
      return $this->continentCodes;
    }

    /**
     * @param string $continentCodes
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setContinentCodes($continentCodes)
    {
      $this->continentCodes = $continentCodes;
      return $this;
    }

    /**
     * @return string
     */
    public function getCountryCodes()
    {
      return $this->countryCodes;
    }

    /**
     * @param string $countryCodes
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setCountryCodes($countryCodes)
    {
      $this->countryCodes = $countryCodes;
      return $this;
    }

    /**
     * @return int
     */
    public function getDurationFrom()
    {
      return $this->durationFrom;
    }

    /**
     * @param int $durationFrom
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setDurationFrom($durationFrom)
    {
      $this->durationFrom = $durationFrom;
      return $this;
    }

    /**
     * @return int
     */
    public function getDurationTo()
    {
      return $this->durationTo;
    }

    /**
     * @param int $durationTo
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setDurationTo($durationTo)
    {
      $this->durationTo = $durationTo;
      return $this;
    }

    /**
     * @return string
     */
    public function getMonths()
    {
      return $this->months;
    }

    /**
     * @param string $months
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setMonths($months)
    {
      $this->months = $months;
      return $this;
    }

    /**
     * @return string[]
     */
    public function getKeywordsAndPhrases()
    {
      return $this->keywordsAndPhrases;
    }

    /**
     * @param string[] $keywordsAndPhrases
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setKeywordsAndPhrases(array $keywordsAndPhrases = null)
    {
      $this->keywordsAndPhrases = $keywordsAndPhrases;
      return $this;
    }

    /**
     * @return string
     */
    public function getPreferedRoomType()
    {
      return $this->preferedRoomType;
    }

    /**
     * @param string $preferedRoomType
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setPreferedRoomType($preferedRoomType)
    {
      $this->preferedRoomType = $preferedRoomType;
      return $this;
    }

    /**
     * @return float
     */
    public function getPriceFrom()
    {
      return $this->priceFrom;
    }

    /**
     * @param float $priceFrom
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setPriceFrom($priceFrom)
    {
      $this->priceFrom = $priceFrom;
      return $this;
    }

    /**
     * @return float
     */
    public function getPriceTo()
    {
      return $this->priceTo;
    }

    /**
     * @param float $priceTo
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setPriceTo($priceTo)
    {
      $this->priceTo = $priceTo;
      return $this;
    }

    /**
     * @return int
     */
    public function getFirstRecordNumber()
    {
      return $this->firstRecordNumber;
    }

    /**
     * @param int $firstRecordNumber
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setFirstRecordNumber($firstRecordNumber)
    {
      $this->firstRecordNumber = $firstRecordNumber;
      return $this;
    }

    /**
     * @return int
     */
    public function getNumberOfRecords()
    {
      return $this->numberOfRecords;
    }

    /**
     * @param int $numberOfRecords
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setNumberOfRecords($numberOfRecords)
    {
      $this->numberOfRecords = $numberOfRecords;
      return $this;
    }

    /**
     * @return string
     */
    public function getOrderBy()
    {
      return $this->orderBy;
    }

    /**
     * @param string $orderBy
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setOrderBy($orderBy)
    {
      $this->orderBy = $orderBy;
      return $this;
    }

    /**
     * @return string
     */
    public function getOrderDirection()
    {
      return $this->orderDirection;
    }

    /**
     * @param string $orderDirection
     * @return \ContentHub\SearchToursBaseRequest
     */
    public function setOrderDirection($orderDirection)
    {
      $this->orderDirection = $orderDirection;
      return $this;
    }

}
