<?php

namespace ContentHub;

class ContinentsVisited
{

    /**
     * @var Continent[] $Continent
     */
    protected $Continent = null;

    /**
     * @param Continent[] $Continent
     */
    public function __construct(array $Continent)
    {
      $this->Continent = $Continent;
    }

    /**
     * @return Continent[]
     */
    public function getContinent()
    {
      return $this->Continent;
    }

    /**
     * @param Continent[] $Continent
     * @return \ContentHub\ContinentsVisited
     */
    public function setContinent(array $Continent)
    {
      $this->Continent = $Continent;
      return $this;
    }

}
