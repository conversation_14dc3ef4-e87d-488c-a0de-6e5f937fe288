<?php

namespace ContentHub;

class Location
{

    /**
     * @var ISO3166CountryCode $CountryCode
     */
    protected $CountryCode = null;

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @param ISO3166CountryCode $CountryCode
     * @param string $Name
     */
    public function __construct($CountryCode, $Name)
    {
      $this->CountryCode = $CountryCode;
      $this->Name = $Name;
    }

    /**
     * @return ISO3166CountryCode
     */
    public function getCountryCode()
    {
      return $this->CountryCode;
    }

    /**
     * @param ISO3166CountryCode $CountryCode
     * @return \ContentHub\Location
     */
    public function setCountryCode($CountryCode)
    {
      $this->CountryCode = $CountryCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\Location
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

}
