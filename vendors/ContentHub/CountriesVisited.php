<?php

namespace ContentHub;

class CountriesVisited
{

    /**
     * @var Country[] $Country
     */
    protected $Country = null;

    /**
     * @param Country[] $Country
     */
    public function __construct(array $Country)
    {
      $this->Country = $Country;
    }

    /**
     * @return Country[]
     */
    public function getCountry()
    {
      return $this->Country;
    }

    /**
     * @param Country[] $Country
     * @return \ContentHub\CountriesVisited
     */
    public function setCountry(array $Country)
    {
      $this->Country = $Country;
      return $this;
    }

}
