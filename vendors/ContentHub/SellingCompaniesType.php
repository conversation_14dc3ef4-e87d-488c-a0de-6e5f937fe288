<?php

namespace ContentHub;

class SellingCompaniesType
{

    /**
     * @var SellingCompanyType[] $SellingCompany
     */
    protected $SellingCompany = null;

    /**
     * @param SellingCompanyType[] $SellingCompany
     */
    public function __construct(array $SellingCompany)
    {
      $this->SellingCompany = $SellingCompany;
    }

    /**
     * @return SellingCompanyType[]
     */
    public function getSellingCompany()
    {
      return $this->SellingCompany;
    }

    /**
     * @param SellingCompanyType[] $SellingCompany
     * @return \ContentHub\SellingCompaniesType
     */
    public function setSellingCompany(array $SellingCompany)
    {
      $this->SellingCompany = $SellingCompany;
      return $this;
    }

}
