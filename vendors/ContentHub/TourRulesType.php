<?php

namespace ContentHub;

class TourRulesType
{

    /**
     * @var PassengersType $Passengers
     */
    protected $Passengers = null;

    /**
     * @var RoomsType $Rooms
     */
    protected $Rooms = null;

    /**
     * @var CombinedIncludedChargesType $CombinedIncludedCharges
     */
    protected $CombinedIncludedCharges = null;

    /**
     * @var boolean $PriceIsIndicative
     */
    protected $PriceIsIndicative = null;

    /**
     * @var DiscountsType $Discounts
     */
    protected $Discounts = null;

    /**
     * @var boolean $IsEligibleForFrequentTravellerDiscount
     */
    protected $IsEligibleForFrequentTravellerDiscount = null;

    /**
     * @var boolean $CanSearchForFlights
     */
    protected $CanSearchForFlights = null;

    /**
     * @param PassengersType $Passengers
     * @param RoomsType $Rooms
     * @param boolean $PriceIsIndicative
     * @param boolean $IsEligibleForFrequentTravellerDiscount
     * @param boolean $CanSearchForFlights
     */
    public function __construct($Passengers, $Rooms, $PriceIsIndicative, $IsEligibleForFrequentTravellerDiscount, $CanSearchForFlights)
    {
      $this->Passengers = $Passengers;
      $this->Rooms = $Rooms;
      $this->PriceIsIndicative = $PriceIsIndicative;
      $this->IsEligibleForFrequentTravellerDiscount = $IsEligibleForFrequentTravellerDiscount;
      $this->CanSearchForFlights = $CanSearchForFlights;
    }

    /**
     * @return PassengersType
     */
    public function getPassengers()
    {
      return $this->Passengers;
    }

    /**
     * @param PassengersType $Passengers
     * @return \ContentHub\TourRulesType
     */
    public function setPassengers($Passengers)
    {
      $this->Passengers = $Passengers;
      return $this;
    }

    /**
     * @return RoomsType
     */
    public function getRooms()
    {
      return $this->Rooms;
    }

    /**
     * @param RoomsType $Rooms
     * @return \ContentHub\TourRulesType
     */
    public function setRooms($Rooms)
    {
      $this->Rooms = $Rooms;
      return $this;
    }

    /**
     * @return CombinedIncludedChargesType
     */
    public function getCombinedIncludedCharges()
    {
      return $this->CombinedIncludedCharges;
    }

    /**
     * @param CombinedIncludedChargesType $CombinedIncludedCharges
     * @return \ContentHub\TourRulesType
     */
    public function setCombinedIncludedCharges($CombinedIncludedCharges)
    {
      $this->CombinedIncludedCharges = $CombinedIncludedCharges;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getPriceIsIndicative()
    {
      return $this->PriceIsIndicative;
    }

    /**
     * @param boolean $PriceIsIndicative
     * @return \ContentHub\TourRulesType
     */
    public function setPriceIsIndicative($PriceIsIndicative)
    {
      $this->PriceIsIndicative = $PriceIsIndicative;
      return $this;
    }

    /**
     * @return DiscountsType
     */
    public function getDiscounts()
    {
      return $this->Discounts;
    }

    /**
     * @param DiscountsType $Discounts
     * @return \ContentHub\TourRulesType
     */
    public function setDiscounts($Discounts)
    {
      $this->Discounts = $Discounts;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getIsEligibleForFrequentTravellerDiscount()
    {
      return $this->IsEligibleForFrequentTravellerDiscount;
    }

    /**
     * @param boolean $IsEligibleForFrequentTravellerDiscount
     * @return \ContentHub\TourRulesType
     */
    public function setIsEligibleForFrequentTravellerDiscount($IsEligibleForFrequentTravellerDiscount)
    {
      $this->IsEligibleForFrequentTravellerDiscount = $IsEligibleForFrequentTravellerDiscount;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getCanSearchForFlights()
    {
      return $this->CanSearchForFlights;
    }

    /**
     * @param boolean $CanSearchForFlights
     * @return \ContentHub\TourRulesType
     */
    public function setCanSearchForFlights($CanSearchForFlights)
    {
      $this->CanSearchForFlights = $CanSearchForFlights;
      return $this;
    }

}
