<?php

namespace ContentHub;

class SellingCompanyType
{

    /**
     * @var DeparturesType $Departures
     */
    protected $Departures = null;

    /**
     * @var string $Code
     */
    protected $Code = null;

    /**
     * @var string $CurrencyCode
     */
    protected $CurrencyCode = null;

    /**
     * @var string $InventoryBrochureCode
     */
    protected $InventoryBrochureCode = null;

    /**
     * @param DeparturesType $Departures
     * @param string $Code
     * @param string $CurrencyCode
     * @param string $InventoryBrochureCode
     */
    public function __construct($Departures, $Code, $CurrencyCode, $InventoryBrochureCode)
    {
      $this->Departures = $Departures;
      $this->Code = $Code;
      $this->CurrencyCode = $CurrencyCode;
      $this->InventoryBrochureCode = $InventoryBrochureCode;
    }

    /**
     * @return DeparturesType
     */
    public function getDepartures()
    {
      return $this->Departures;
    }

    /**
     * @param DeparturesType $Departures
     * @return \ContentHub\SellingCompanyType
     */
    public function setDepartures($Departures)
    {
      $this->Departures = $Departures;
      return $this;
    }

    /**
     * @return string
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param string $Code
     * @return \ContentHub\SellingCompanyType
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getCurrencyCode()
    {
      return $this->CurrencyCode;
    }

    /**
     * @param string $CurrencyCode
     * @return \ContentHub\SellingCompanyType
     */
    public function setCurrencyCode($CurrencyCode)
    {
      $this->CurrencyCode = $CurrencyCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getInventoryBrochureCode()
    {
      return $this->InventoryBrochureCode;
    }

    /**
     * @param string $InventoryBrochureCode
     * @return \ContentHub\SellingCompanyType
     */
    public function setInventoryBrochureCode($InventoryBrochureCode)
    {
      $this->InventoryBrochureCode = $InventoryBrochureCode;
      return $this;
    }

}
