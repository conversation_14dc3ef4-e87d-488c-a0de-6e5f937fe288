<?php

namespace ContentHub;

class a
{

    /**
     * @var string $_
     */
    protected $_ = null;

    /**
     * @var anyURI $href
     */
    protected $href = null;

    /**
     * @param string $_
     * @param anyURI $href
     */
    public function __construct($_, $href)
    {
      $this->_ = $_;
      $this->href = $href;
    }

    /**
     * @return string
     */
    public function get_()
    {
      return $this->_;
    }

    /**
     * @param string $_
     * @return \ContentHub\a
     */
    public function set_($_)
    {
      $this->_ = $_;
      return $this;
    }

    /**
     * @return anyURI
     */
    public function getHref()
    {
      return $this->href;
    }

    /**
     * @param anyURI $href
     * @return \ContentHub\a
     */
    public function setHref($href)
    {
      $this->href = $href;
      return $this;
    }

}
