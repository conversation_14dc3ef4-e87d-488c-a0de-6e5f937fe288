<?php

namespace ContentHub;

class KeywordsPhrases
{

    /**
     * @var Text[] $Text
     */
    protected $Text = null;

    /**
     * @param Text[] $Text
     */
    public function __construct(array $Text)
    {
      $this->Text = $Text;
    }

    /**
     * @return Text[]
     */
    public function getText()
    {
      return $this->Text;
    }

    /**
     * @param Text[] $Text
     * @return \ContentHub\KeywordsPhrases
     */
    public function setText(array $Text)
    {
      $this->Text = $Text;
      return $this;
    }

}
