<?php

namespace ContentHub;

class ProductRoomType
{

    /**
     * @var RoomTypeCodeType $Type
     */
    protected $Type = null;

    /**
     * @var PriceType $Price
     */
    protected $Price = null;

    /**
     * @param RoomTypeCodeType $Type
     * @param PriceType $Price
     */
    public function __construct($Type, $Price)
    {
      $this->Type = $Type;
      $this->Price = $Price;
    }

    /**
     * @return RoomTypeCodeType
     */
    public function getType()
    {
      return $this->Type;
    }

    /**
     * @param RoomTypeCodeType $Type
     * @return \ContentHub\ProductRoomType
     */
    public function setType($Type)
    {
      $this->Type = $Type;
      return $this;
    }

    /**
     * @return PriceType
     */
    public function getPrice()
    {
      return $this->Price;
    }

    /**
     * @param PriceType $Price
     * @return \ContentHub\ProductRoomType
     */
    public function setPrice($Price)
    {
      $this->Price = $Price;
      return $this;
    }

}
