<?php

namespace ContentHub;

class li
{

    /**
     * @var string $values
     */
    protected $values = null;

    /**
     * @var a $a
     */
    protected $a = null;

    /**
     * @var strong $strong
     */
    protected $strong = null;

    /**
     * @param string $values
     * @param a $a
     * @param strong $strong
     */
    public function __construct($values, $a, $strong)
    {
      $this->values = $values;
      $this->a = $a;
      $this->strong = $strong;
    }

    /**
     * @return string
     */
    public function getValues()
    {
      return $this->values;
    }

    /**
     * @param string $values
     * @return \ContentHub\li
     */
    public function setValues($values)
    {
      $this->values = $values;
      return $this;
    }

    /**
     * @return a
     */
    public function getA()
    {
      return $this->a;
    }

    /**
     * @param a $a
     * @return \ContentHub\li
     */
    public function setA($a)
    {
      $this->a = $a;
      return $this;
    }

    /**
     * @return strong
     */
    public function getStrong()
    {
      return $this->strong;
    }

    /**
     * @param strong $strong
     * @return \ContentHub\li
     */
    public function setStrong($strong)
    {
      $this->strong = $strong;
      return $this;
    }

}
