<?php

namespace ContentHub;

class Itinerary
{

    /**
     * @var ItinerarySegment[] $ItinerarySegment
     */
    protected $ItinerarySegment = null;

    /**
     * @param ItinerarySegment[] $ItinerarySegment
     */
    public function __construct(array $ItinerarySegment)
    {
      $this->ItinerarySegment = $ItinerarySegment;
    }

    /**
     * @return ItinerarySegment[]
     */
    public function getItinerarySegment()
    {
      return $this->ItinerarySegment;
    }

    /**
     * @param ItinerarySegment[] $ItinerarySegment
     * @return \ContentHub\Itinerary
     */
    public function setItinerarySegment(array $ItinerarySegment)
    {
      $this->ItinerarySegment = $ItinerarySegment;
      return $this;
    }

}
