<?php

namespace ContentHub;

class Text
{

    /**
     * @var string $values
     */
    protected $values = null;

    /**
     * @var a $a
     */
    protected $a = null;

    /**
     * @var br $br
     */
    protected $br = null;

    /**
     * @var strong $strong
     */
    protected $strong = null;

    /**
     * @var ul $ul
     */
    protected $ul = null;

    /**
     * @param string $values
     * @param a $a
     * @param br $br
     * @param strong $strong
     * @param ul $ul
     */
    public function __construct($values, $a, $br, $strong, $ul)
    {
      $this->values = $values;
      $this->a = $a;
      $this->br = $br;
      $this->strong = $strong;
      $this->ul = $ul;
    }

    /**
     * @return string
     */
    public function getValues()
    {
      return $this->values;
    }

    /**
     * @param string $values
     * @return \ContentHub\Text
     */
    public function setValues($values)
    {
      $this->values = $values;
      return $this;
    }

    /**
     * @return a
     */
    public function getA()
    {
      return $this->a;
    }

    /**
     * @param a $a
     * @return \ContentHub\Text
     */
    public function setA($a)
    {
      $this->a = $a;
      return $this;
    }

    /**
     * @return br
     */
    public function getBr()
    {
      return $this->br;
    }

    /**
     * @param br $br
     * @return \ContentHub\Text
     */
    public function setBr($br)
    {
      $this->br = $br;
      return $this;
    }

    /**
     * @return strong
     */
    public function getStrong()
    {
      return $this->strong;
    }

    /**
     * @param strong $strong
     * @return \ContentHub\Text
     */
    public function setStrong($strong)
    {
      $this->strong = $strong;
      return $this;
    }

    /**
     * @return ul
     */
    public function getUl()
    {
      return $this->ul;
    }

    /**
     * @param ul $ul
     * @return \ContentHub\Text
     */
    public function setUl($ul)
    {
      $this->ul = $ul;
      return $this;
    }

}
