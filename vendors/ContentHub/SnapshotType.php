<?php

namespace ContentHub;

class SnapshotType
{

    /**
     * @var string $fileName
     */
    protected $fileName = null;

    /**
     * @var string $fileNameDesc
     */
    protected $fileNameDesc = null;

    /**
     * @var string $message
     */
    protected $message = null;

    /**
     * @var string $typeMsg
     */
    protected $typeMsg = null;

    /**
     * @var int $countFile
     */
    protected $countFile = null;

    /**
     * @var string $brandCode
     */
    protected $brandCode = null;

    /**
     * @var \DateTime $dateUpdate
     */
    protected $dateUpdate = null;

    /**
     * @var int $noProcessingFile
     */
    protected $noProcessingFile = null;

    /**
     * @param string $brandCode
     * @param \DateTime $dateUpdate
     */
    public function __construct($brandCode, \DateTime $dateUpdate)
    {
      $this->brandCode = $brandCode;
      $this->dateUpdate = $dateUpdate->format(\DateTime::ATOM);
    }

    /**
     * @return string
     */
    public function getFileName()
    {
      return $this->fileName;
    }

    /**
     * @param string $fileName
     * @return \ContentHub\SnapshotType
     */
    public function setFileName($fileName)
    {
      $this->fileName = $fileName;
      return $this;
    }

    /**
     * @return string
     */
    public function getFileNameDesc()
    {
      return $this->fileNameDesc;
    }

    /**
     * @param string $fileNameDesc
     * @return \ContentHub\SnapshotType
     */
    public function setFileNameDesc($fileNameDesc)
    {
      $this->fileNameDesc = $fileNameDesc;
      return $this;
    }

    /**
     * @return string
     */
    public function getMessage()
    {
      return $this->message;
    }

    /**
     * @param string $message
     * @return \ContentHub\SnapshotType
     */
    public function setMessage($message)
    {
      $this->message = $message;
      return $this;
    }

    /**
     * @return string
     */
    public function getTypeMsg()
    {
      return $this->typeMsg;
    }

    /**
     * @param string $typeMsg
     * @return \ContentHub\SnapshotType
     */
    public function setTypeMsg($typeMsg)
    {
      $this->typeMsg = $typeMsg;
      return $this;
    }

    /**
     * @return int
     */
    public function getCountFile()
    {
      return $this->countFile;
    }

    /**
     * @param int $countFile
     * @return \ContentHub\SnapshotType
     */
    public function setCountFile($countFile)
    {
      $this->countFile = $countFile;
      return $this;
    }

    /**
     * @return string
     */
    public function getBrandCode()
    {
      return $this->brandCode;
    }

    /**
     * @param string $brandCode
     * @return \ContentHub\SnapshotType
     */
    public function setBrandCode($brandCode)
    {
      $this->brandCode = $brandCode;
      return $this;
    }

    /**
     * @return \DateTime
     */
    public function getDateUpdate()
    {
      if ($this->dateUpdate == null) {
        return null;
      } else {
        try {
          return new \DateTime($this->dateUpdate);
        } catch (\Exception $e) {
          return false;
        }
      }
    }

    /**
     * @param \DateTime $dateUpdate
     * @return \ContentHub\SnapshotType
     */
    public function setDateUpdate(\DateTime $dateUpdate)
    {
      $this->dateUpdate = $dateUpdate->format(\DateTime::ATOM);
      return $this;
    }

    /**
     * @return int
     */
    public function getNoProcessingFile()
    {
      return $this->noProcessingFile;
    }

    /**
     * @param int $noProcessingFile
     * @return \ContentHub\SnapshotType
     */
    public function setNoProcessingFile($noProcessingFile)
    {
      $this->noProcessingFile = $noProcessingFile;
      return $this;
    }

}
