<?php

namespace ContentHub;

class SellingCompanies
{

    /**
     * @var SellingCompany[] $SellingCompany
     */
    protected $SellingCompany = null;

    /**
     * @param SellingCompany[] $SellingCompany
     */
    public function __construct(array $SellingCompany)
    {
      $this->SellingCompany = $SellingCompany;
    }

    /**
     * @return SellingCompany[]
     */
    public function getSellingCompany()
    {
      return $this->SellingCompany;
    }

    /**
     * @param SellingCompany[] $SellingCompany
     * @return \ContentHub\SellingCompanies
     */
    public function setSellingCompany(array $SellingCompany)
    {
      $this->SellingCompany = $SellingCompany;
      return $this;
    }

}
