<?php

namespace ContentHub;

class GetContinentsAndCountriesVisitedRequest
{

    /**
     * @var string $securityKey
     */
    protected $securityKey = null;

    /**
     * @var string $continent
     */
    protected $continent = null;

    /**
     * @var string $sellingCompanies
     */
    protected $sellingCompanies = null;

    /**
     * @param string $securityKey
     * @param string $continent
     * @param string $sellingCompanies
     */
    public function __construct($securityKey, $continent, $sellingCompanies)
    {
      $this->securityKey = $securityKey;
      $this->continent = $continent;
      $this->sellingCompanies = $sellingCompanies;
    }

    /**
     * @return string
     */
    public function getSecurityKey()
    {
      return $this->securityKey;
    }

    /**
     * @param string $securityKey
     * @return \ContentHub\GetContinentsAndCountriesVisitedRequest
     */
    public function setSecurityKey($securityKey)
    {
      $this->securityKey = $securityKey;
      return $this;
    }

    /**
     * @return string
     */
    public function getContinent()
    {
      return $this->continent;
    }

    /**
     * @param string $continent
     * @return \ContentHub\GetContinentsAndCountriesVisitedRequest
     */
    public function setContinent($continent)
    {
      $this->continent = $continent;
      return $this;
    }

    /**
     * @return string
     */
    public function getSellingCompanies()
    {
      return $this->sellingCompanies;
    }

    /**
     * @param string $sellingCompanies
     * @return \ContentHub\GetContinentsAndCountriesVisitedRequest
     */
    public function setSellingCompanies($sellingCompanies)
    {
      $this->sellingCompanies = $sellingCompanies;
      return $this;
    }

}
