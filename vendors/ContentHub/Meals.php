<?php

namespace ContentHub;

class Meals
{

    /**
     * @var Meal[] $Meal
     */
    protected $Meal = null;

    /**
     * @param Meal[] $Meal
     */
    public function __construct(array $Meal)
    {
      $this->Meal = $Meal;
    }

    /**
     * @return Meal[]
     */
    public function getMeal()
    {
      return $this->Meal;
    }

    /**
     * @param Meal[] $Meal
     * @return \ContentHub\Meals
     */
    public function setMeal(array $Meal)
    {
      $this->Meal = $Meal;
      return $this;
    }

}
