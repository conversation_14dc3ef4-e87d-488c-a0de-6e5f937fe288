<?php

namespace ContentHub;

class IncludedSubProduct
{

    /**
     * @var string $Code
     */
    protected $Code = null;

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @var string $Category
     */
    protected $Category = null;

    /**
     * @var string $ServiceType
     */
    protected $ServiceType = null;

    /**
     * @param string $Code
     * @param string $Name
     * @param string $Category
     * @param string $ServiceType
     */
    public function __construct($Code, $Name, $Category, $ServiceType)
    {
      $this->Code = $Code;
      $this->Name = $Name;
      $this->Category = $Category;
      $this->ServiceType = $ServiceType;
    }

    /**
     * @return string
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param string $Code
     * @return \ContentHub\IncludedSubProduct
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\IncludedSubProduct
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

    /**
     * @return string
     */
    public function getCategory()
    {
      return $this->Category;
    }

    /**
     * @param string $Category
     * @return \ContentHub\IncludedSubProduct
     */
    public function setCategory($Category)
    {
      $this->Category = $Category;
      return $this;
    }

    /**
     * @return string
     */
    public function getServiceType()
    {
      return $this->ServiceType;
    }

    /**
     * @param string $ServiceType
     * @return \ContentHub\IncludedSubProduct
     */
    public function setServiceType($ServiceType)
    {
      $this->ServiceType = $ServiceType;
      return $this;
    }

}
