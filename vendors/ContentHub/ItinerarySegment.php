<?php

namespace ContentHub;

class ItinerarySegment
{

    /**
     * @var string $Title
     */
    protected $Title = null;

    /**
     * @var Text $Text
     */
    protected $Text = null;

    /**
     * @var string $Accommodation
     */
    protected $Accommodation = null;

    /**
     * @var Meals $Meals
     */
    protected $Meals = null;

    /**
     * @var LocationsVisited $LocationsVisited
     */
    protected $LocationsVisited = null;

    /**
     * @var OptionalExtras $OptionalExtras
     */
    protected $OptionalExtras = null;

    /**
     * @var int $Duration
     */
    protected $Duration = null;

    /**
     * @var int $StartDay
     */
    protected $StartDay = null;

    /**
     * @param string $Title
     * @param array $Text
     * @param int $Duration
     * @param int $StartDay
     */
    public function __construct($Title, $Text, $Duration, $StartDay)
    {
      $this->Title = $Title;
      $this->Duration = $Duration;
      $this->StartDay = $StartDay;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
      return $this->Title;
    }

    /**
     * @param string $Title
     * @return \ContentHub\ItinerarySegment
     */
    public function setTitle($Title)
    {
      $this->Title = $Title;
      return $this;
    }

    /**
     * @return Text
     */
    public function getText()
    {
      return $this->Text;
    }

    /**
     * @param Text $Text
     * @return \ContentHub\ItinerarySegment
     */
    public function setText($Text)
    {
      $this->Text = $Text;
      return $this;
    }

    /**
     * @return string
     */
    public function getAccommodation()
    {
      return $this->Accommodation;
    }

    /**
     * @param string $Accommodation
     * @return \ContentHub\ItinerarySegment
     */
    public function setAccommodation($Accommodation)
    {
      $this->Accommodation = $Accommodation;
      return $this;
    }

    /**
     * @return Meals
     */
    public function getMeals()
    {
      return $this->Meals;
    }

    /**
     * @param Meals $Meals
     * @return \ContentHub\ItinerarySegment
     */
    public function setMeals($Meals)
    {
      $this->Meals = $Meals;
      return $this;
    }

    /**
     * @return LocationsVisited
     */
    public function getLocationsVisited()
    {
      return $this->LocationsVisited;
    }

    /**
     * @param LocationsVisited $LocationsVisited
     * @return \ContentHub\ItinerarySegment
     */
    public function setLocationsVisited($LocationsVisited)
    {
      $this->LocationsVisited = $LocationsVisited;
      return $this;
    }

    /**
     * @return OptionalExtras
     */
    public function getOptionalExtras()
    {
      return $this->OptionalExtras;
    }

    /**
     * @param OptionalExtras $OptionalExtras
     * @return \ContentHub\ItinerarySegment
     */
    public function setOptionalExtras($OptionalExtras)
    {
      $this->OptionalExtras = $OptionalExtras;
      return $this;
    }

    /**
     * @return int
     */
    public function getDuration()
    {
      return $this->Duration;
    }

    /**
     * @param int $Duration
     * @return \ContentHub\ItinerarySegment
     */
    public function setDuration($Duration)
    {
      $this->Duration = $Duration;
      return $this;
    }

    /**
     * @return int
     */
    public function getStartDay()
    {
      return $this->StartDay;
    }

    /**
     * @param int $StartDay
     * @return \ContentHub\ItinerarySegment
     */
    public function setStartDay($StartDay)
    {
      $this->StartDay = $StartDay;
      return $this;
    }

}
