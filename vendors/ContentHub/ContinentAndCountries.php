<?php

namespace ContentHub;

class ContinentAndCountries
{

    /**
     * @var string $continent
     */
    protected $continent = null;

    /**
     * @var string[] $countries
     */
    protected $countries = null;

    /**
     * @param string $continent
     * @param string[] $countries
     */
    public function __construct($continent, array $countries)
    {
      $this->continent = $continent;
      $this->countries = $countries;
    }

    /**
     * @return string
     */
    public function getContinent()
    {
      return $this->continent;
    }

    /**
     * @param string $continent
     * @return \ContentHub\ContinentAndCountries
     */
    public function setContinent($continent)
    {
      $this->continent = $continent;
      return $this;
    }

    /**
     * @return string[]
     */
    public function getCountries()
    {
      return $this->countries;
    }

    /**
     * @param string[] $countries
     * @return \ContentHub\ContinentAndCountries
     */
    public function setCountries(array $countries)
    {
      $this->countries = $countries;
      return $this;
    }

}
