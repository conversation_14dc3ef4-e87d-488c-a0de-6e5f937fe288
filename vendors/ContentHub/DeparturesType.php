<?php

namespace ContentHub;

class DeparturesType
{

    /**
     * @var DepartureType[] $Departure
     */
    protected $Departure = null;

    /**
     * @param DepartureType[] $Departure
     */
    public function __construct(array $Departure)
    {
      $this->Departure = $Departure;
    }

    /**
     * @return DepartureType[]
     */
    public function getDeparture()
    {
      return $this->Departure;
    }

    /**
     * @param DepartureType[] $Departure
     * @return \ContentHub\DeparturesType
     */
    public function setDeparture(array $Departure)
    {
      $this->Departure = $Departure;
      return $this;
    }

}
