<?php

namespace ContentHub;

class SearchToursResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    /**
     * @var boolean $successful
     */
    protected $successful = null;

    /**
     * @var int $totalRecords
     */
    protected $totalRecords = null;

    /**
     * @var int $numberOfRecords
     */
    protected $numberOfRecords = null;

    /**
     * @var string $subsetReturned
     */
    protected $subsetReturned = null;

    /**
     * @var SearchResults $searchResults
     */
    protected $searchResults = null;

    /**
     * @param MessageContext $messageContext
     * @param boolean $successful
     * @param int $totalRecords
     * @param int $numberOfRecords
     * @param string $subsetReturned
     * @param SearchResults $searchResults
     */
    public function __construct($messageContext, $successful, $totalRecords, $numberOfRecords, $subsetReturned, $searchResults)
    {
      $this->messageContext = $messageContext;
      $this->successful = $successful;
      $this->totalRecords = $totalRecords;
      $this->numberOfRecords = $numberOfRecords;
      $this->subsetReturned = $subsetReturned;
      $this->searchResults = $searchResults;
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\SearchToursResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getSuccessful()
    {
      return $this->successful;
    }

    /**
     * @param boolean $successful
     * @return \ContentHub\SearchToursResponse
     */
    public function setSuccessful($successful)
    {
      $this->successful = $successful;
      return $this;
    }

    /**
     * @return int
     */
    public function getTotalRecords()
    {
      return $this->totalRecords;
    }

    /**
     * @param int $totalRecords
     * @return \ContentHub\SearchToursResponse
     */
    public function setTotalRecords($totalRecords)
    {
      $this->totalRecords = $totalRecords;
      return $this;
    }

    /**
     * @return int
     */
    public function getNumberOfRecords()
    {
      return $this->numberOfRecords;
    }

    /**
     * @param int $numberOfRecords
     * @return \ContentHub\SearchToursResponse
     */
    public function setNumberOfRecords($numberOfRecords)
    {
      $this->numberOfRecords = $numberOfRecords;
      return $this;
    }

    /**
     * @return string
     */
    public function getSubsetReturned()
    {
      return $this->subsetReturned;
    }

    /**
     * @param string $subsetReturned
     * @return \ContentHub\SearchToursResponse
     */
    public function setSubsetReturned($subsetReturned)
    {
      $this->subsetReturned = $subsetReturned;
      return $this;
    }

    /**
     * @return SearchResults
     */
    public function getSearchResults()
    {
      return $this->searchResults;
    }

    /**
     * @param SearchResults $searchResults
     * @return \ContentHub\SearchToursResponse
     */
    public function setSearchResults($searchResults)
    {
      $this->searchResults = $searchResults;
      return $this;
    }

}
