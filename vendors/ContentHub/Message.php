<?php

namespace ContentHub;

class Message
{

    /**
     * @var string $interpolatedMessage
     */
    protected $interpolatedMessage = null;

    /**
     * @var MessageType $messageType
     */
    protected $messageType = null;

    /**
     * @var MessageSubject[] $messageSubject
     */
    protected $messageSubject = null;

    /**
     * @param MessageType $messageType
     */
    public function __construct($messageType)
    {
      $this->messageType = $messageType;
    }

    /**
     * @return string
     */
    public function getInterpolatedMessage()
    {
      return $this->interpolatedMessage;
    }

    /**
     * @param string $interpolatedMessage
     * @return \ContentHub\Message
     */
    public function setInterpolatedMessage($interpolatedMessage)
    {
      $this->interpolatedMessage = $interpolatedMessage;
      return $this;
    }

    /**
     * @return MessageType
     */
    public function getMessageType()
    {
      return $this->messageType;
    }

    /**
     * @param MessageType $messageType
     * @return \ContentHub\Message
     */
    public function setMessageType($messageType)
    {
      $this->messageType = $messageType;
      return $this;
    }

    /**
     * @return MessageSubject[]
     */
    public function getMessageSubject()
    {
      return $this->messageSubject;
    }

    /**
     * @param MessageSubject[] $messageSubject
     * @return \ContentHub\Message
     */
    public function setMessageSubject(array $messageSubject = null)
    {
      $this->messageSubject = $messageSubject;
      return $this;
    }

}
