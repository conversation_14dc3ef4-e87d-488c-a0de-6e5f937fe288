<?php

namespace ContentHub;

class OccupancyRuleType
{

    /**
     * @var int $MaximumAdults
     */
    protected $MaximumAdults = null;

    /**
     * @var int $MaximumPassengers
     */
    protected $MaximumPassengers = null;

    /**
     * @var int $MinimumAdults
     */
    protected $MinimumAdults = null;

    /**
     * @var int $MinimumPassengers
     */
    protected $MinimumPassengers = null;

    /**
     * @var int $MinimumPayingAdults
     */
    protected $MinimumPayingAdults = null;

    /**
     * @param int $MaximumAdults
     * @param int $MaximumPassengers
     * @param int $MinimumAdults
     * @param int $MinimumPassengers
     * @param int $MinimumPayingAdults
     */
    public function __construct($MaximumAdults, $MaximumPassengers, $MinimumAdults, $MinimumPassengers, $MinimumPayingAdults)
    {
      $this->MaximumAdults = $MaximumAdults;
      $this->MaximumPassengers = $MaximumPassengers;
      $this->MinimumAdults = $MinimumAdults;
      $this->MinimumPassengers = $MinimumPassengers;
      $this->MinimumPayingAdults = $MinimumPayingAdults;
    }

    /**
     * @return int
     */
    public function getMaximumAdults()
    {
      return $this->MaximumAdults;
    }

    /**
     * @param int $MaximumAdults
     * @return \ContentHub\OccupancyRuleType
     */
    public function setMaximumAdults($MaximumAdults)
    {
      $this->MaximumAdults = $MaximumAdults;
      return $this;
    }

    /**
     * @return int
     */
    public function getMaximumPassengers()
    {
      return $this->MaximumPassengers;
    }

    /**
     * @param int $MaximumPassengers
     * @return \ContentHub\OccupancyRuleType
     */
    public function setMaximumPassengers($MaximumPassengers)
    {
      $this->MaximumPassengers = $MaximumPassengers;
      return $this;
    }

    /**
     * @return int
     */
    public function getMinimumAdults()
    {
      return $this->MinimumAdults;
    }

    /**
     * @param int $MinimumAdults
     * @return \ContentHub\OccupancyRuleType
     */
    public function setMinimumAdults($MinimumAdults)
    {
      $this->MinimumAdults = $MinimumAdults;
      return $this;
    }

    /**
     * @return int
     */
    public function getMinimumPassengers()
    {
      return $this->MinimumPassengers;
    }

    /**
     * @param int $MinimumPassengers
     * @return \ContentHub\OccupancyRuleType
     */
    public function setMinimumPassengers($MinimumPassengers)
    {
      $this->MinimumPassengers = $MinimumPassengers;
      return $this;
    }

    /**
     * @return int
     */
    public function getMinimumPayingAdults()
    {
      return $this->MinimumPayingAdults;
    }

    /**
     * @param int $MinimumPayingAdults
     * @return \ContentHub\OccupancyRuleType
     */
    public function setMinimumPayingAdults($MinimumPayingAdults)
    {
      $this->MinimumPayingAdults = $MinimumPayingAdults;
      return $this;
    }

}
