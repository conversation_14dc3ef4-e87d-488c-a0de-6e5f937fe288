<?php

namespace ContentHub;

class RoomPriceType
{

    /**
     * @var Adult $Adult
     */
    protected $Adult = null;

    /**
     * @var Child $Child
     */
    protected $Child = null;

    /**
     * @param Adult $Adult
     * @param Child $Child
     */
    public function __construct($Adult, $Child)
    {
      $this->Adult = $Adult;
      $this->Child = $Child;
    }

    /**
     * @return Adult
     */
    public function getAdult()
    {
      return $this->Adult;
    }

    /**
     * @param Adult $Adult
     * @return \ContentHub\RoomPriceType
     */
    public function setAdult($Adult)
    {
      $this->Adult = $Adult;
      return $this;
    }

    /**
     * @return Child
     */
    public function getChild()
    {
      return $this->Child;
    }

    /**
     * @param Child $Child
     * @return \ContentHub\RoomPriceType
     */
    public function setChild($Child)
    {
      $this->Child = $Child;
      return $this;
    }

}
