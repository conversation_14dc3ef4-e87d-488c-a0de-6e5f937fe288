<?php

namespace ContentHub;

class MiscellaneousProductType
{

    /**
     * @var string $Code
     */
    protected $Code = null;

    /**
     * @var string $Category
     */
    protected $Category = null;

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @var PriceType $Price
     */
    protected $Price = null;

    /**
     * @param string $Code
     * @param string $Category
     * @param string $Name
     * @param PriceType $Price
     */
    public function __construct($Code, $Category, $Name, $Price)
    {
      $this->Code = $Code;
      $this->Category = $Category;
      $this->Name = $Name;
      $this->Price = $Price;
    }

    /**
     * @return string
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param string $Code
     * @return \ContentHub\MiscellaneousProductType
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getCategory()
    {
      return $this->Category;
    }

    /**
     * @param string $Category
     * @return \ContentHub\MiscellaneousProductType
     */
    public function setCategory($Category)
    {
      $this->Category = $Category;
      return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\MiscellaneousProductType
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

    /**
     * @return PriceType
     */
    public function getPrice()
    {
      return $this->Price;
    }

    /**
     * @param PriceType $Price
     * @return \ContentHub\MiscellaneousProductType
     */
    public function setPrice($Price)
    {
      $this->Price = $Price;
      return $this;
    }

}
