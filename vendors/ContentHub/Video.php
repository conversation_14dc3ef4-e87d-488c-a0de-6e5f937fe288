<?php

namespace ContentHub;

class Video
{

    /**
     * @var NMTOKEN $Name
     */
    protected $Name = null;

    /**
     * @var string $Caption
     */
    protected $Caption = null;

    /**
     * @var ratiostring $AspectRatio
     */
    protected $AspectRatio = null;

    /**
     * @var NMTOKEN $Type
     */
    protected $Type = null;

    /**
     * @var anyURI $Url
     */
    protected $Url = null;

    /**
     * @param NMTOKEN $Name
     * @param string $Caption
     * @param ratiostring $AspectRatio
     * @param NMTOKEN $Type
     * @param anyURI $Url
     */
    public function __construct($Name, $Caption, $AspectRatio, $Type, $Url)
    {
      $this->Name = $Name;
      $this->Caption = $Caption;
      $this->AspectRatio = $AspectRatio;
      $this->Type = $Type;
      $this->Url = $Url;
    }

    /**
     * @return NMTOKEN
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param NMTOKEN $Name
     * @return \ContentHub\Video
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

    /**
     * @return string
     */
    public function getCaption()
    {
      return $this->Caption;
    }

    /**
     * @param string $Caption
     * @return \ContentHub\Video
     */
    public function setCaption($Caption)
    {
      $this->Caption = $Caption;
      return $this;
    }

    /**
     * @return ratiostring
     */
    public function getAspectRatio()
    {
      return $this->AspectRatio;
    }

    /**
     * @param ratiostring $AspectRatio
     * @return \ContentHub\Video
     */
    public function setAspectRatio($AspectRatio)
    {
      $this->AspectRatio = $AspectRatio;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getType()
    {
      return $this->Type;
    }

    /**
     * @param NMTOKEN $Type
     * @return \ContentHub\Video
     */
    public function setType($Type)
    {
      $this->Type = $Type;
      return $this;
    }

    /**
     * @return anyURI
     */
    public function getUrl()
    {
      return $this->Url;
    }

    /**
     * @param anyURI $Url
     * @return \ContentHub\Video
     */
    public function setUrl($Url)
    {
      $this->Url = $Url;
      return $this;
    }

}
