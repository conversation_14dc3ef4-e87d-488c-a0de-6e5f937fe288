<?php

namespace ContentHub;

class GetTourDataUploadStatusRequest
{

    /**
     * @var string $securityKey
     */
    protected $securityKey = null;

    /**
     * @var string $brandCode
     */
    protected $brandCode = null;

    /**
     * @var string $fileName
     */
    protected $fileName = null;

    /**
     * @param string $securityKey
     * @param string $brandCode
     * @param string $fileName
     */
    public function __construct($securityKey, $brandCode, $fileName)
    {
      $this->securityKey = $securityKey;
      $this->brandCode = $brandCode;
      $this->fileName = $fileName;
    }

    /**
     * @return string
     */
    public function getSecurityKey()
    {
      return $this->securityKey;
    }

    /**
     * @param string $securityKey
     * @return \ContentHub\GetTourDataUploadStatusRequest
     */
    public function setSecurityKey($securityKey)
    {
      $this->securityKey = $securityKey;
      return $this;
    }

    /**
     * @return string
     */
    public function getBrandCode()
    {
      return $this->brandCode;
    }

    /**
     * @param string $brandCode
     * @return \ContentHub\GetTourDataUploadStatusRequest
     */
    public function setBrandCode($brandCode)
    {
      $this->brandCode = $brandCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getFileName()
    {
      return $this->fileName;
    }

    /**
     * @param string $fileName
     * @return \ContentHub\GetTourDataUploadStatusRequest
     */
    public function setFileName($fileName)
    {
      $this->fileName = $fileName;
      return $this;
    }

}
