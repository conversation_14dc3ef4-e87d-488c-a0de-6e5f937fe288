<?php

namespace ContentHub;

class TourCategories
{

    /**
     * @var TourCategory $TourCategory
     */
    protected $TourCategory = null;

    /**
     * @param TourCategory $TourCategory
     */
    public function __construct($TourCategory)
    {
      $this->TourCategory = $TourCategory;
    }

    /**
     * @return TourCategory
     */
    public function getTourCategory()
    {
      return $this->TourCategory;
    }

    /**
     * @param TourCategory $TourCategory
     * @return \ContentHub\TourCategories
     */
    public function setTourCategory($TourCategory)
    {
      $this->TourCategory = $TourCategory;
      return $this;
    }

}
