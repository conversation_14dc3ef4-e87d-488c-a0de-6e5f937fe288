<?php

namespace ContentHub;

class RoomsType
{

    /**
     * @var RoomType[] $Room
     */
    protected $Room = null;

    /**
     * @param RoomType[] $Room
     */
    public function __construct(array $Room)
    {
      $this->Room = $Room;
    }

    /**
     * @return RoomType[]
     */
    public function getRoom()
    {
      return $this->Room;
    }

    /**
     * @param RoomType[] $Room
     * @return \ContentHub\RoomsType
     */
    public function setRoom(array $Room)
    {
      $this->Room = $Room;
      return $this;
    }

}
