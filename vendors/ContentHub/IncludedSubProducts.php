<?php

namespace ContentHub;

class IncludedSubProducts
{

    /**
     * @var IncludedSubProduct[] $IncludedSubProduct
     */
    protected $IncludedSubProduct = null;

    /**
     * @param IncludedSubProduct[] $IncludedSubProduct
     */
    public function __construct(array $IncludedSubProduct)
    {
      $this->IncludedSubProduct = $IncludedSubProduct;
    }

    /**
     * @return IncludedSubProduct[]
     */
    public function getIncludedSubProduct()
    {
      return $this->IncludedSubProduct;
    }

    /**
     * @param IncludedSubProduct[] $IncludedSubProduct
     * @return \ContentHub\IncludedSubProducts
     */
    public function setIncludedSubProduct(array $IncludedSubProduct)
    {
      $this->IncludedSubProduct = $IncludedSubProduct;
      return $this;
    }

}
