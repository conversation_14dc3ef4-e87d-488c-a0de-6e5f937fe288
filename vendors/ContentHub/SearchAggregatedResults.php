<?php

namespace ContentHub;

class SearchAggregatedResults
{

    /**
     * @var string $cataloguedTourCode
     */
    protected $cataloguedTourCode = null;

    /**
     * @var string $cataloguedTourName
     */
    protected $cataloguedTourName = null;

    /**
     * @var SearchAggregatedSubResults[] $searchAggregatedSubResults
     */
    protected $searchAggregatedSubResults = null;

    /**
     * @param SearchAggregatedSubResults[] $searchAggregatedSubResults
     */
    public function __construct(array $searchAggregatedSubResults)
    {
      $this->searchAggregatedSubResults = $searchAggregatedSubResults;
    }

    /**
     * @return string
     */
    public function getCataloguedTourCode()
    {
      return $this->cataloguedTourCode;
    }

    /**
     * @param string $cataloguedTourCode
     * @return \ContentHub\SearchAggregatedResults
     */
    public function setCataloguedTourCode($cataloguedTourCode)
    {
      $this->cataloguedTourCode = $cataloguedTourCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getCataloguedTourName()
    {
      return $this->cataloguedTourName;
    }

    /**
     * @param string $cataloguedTourName
     * @return \ContentHub\SearchAggregatedResults
     */
    public function setCataloguedTourName($cataloguedTourName)
    {
      $this->cataloguedTourName = $cataloguedTourName;
      return $this;
    }

    /**
     * @return SearchAggregatedSubResults[]
     */
    public function getSearchAggregatedSubResults()
    {
      return $this->searchAggregatedSubResults;
    }

    /**
     * @param SearchAggregatedSubResults[] $searchAggregatedSubResults
     * @return \ContentHub\SearchAggregatedResults
     */
    public function setSearchAggregatedSubResults(array $searchAggregatedSubResults)
    {
      $this->searchAggregatedSubResults = $searchAggregatedSubResults;
      return $this;
    }

}
