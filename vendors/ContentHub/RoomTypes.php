<?php

namespace ContentHub;

class RoomTypes
{

    /**
     * @var RoomType[] $RoomType
     */
    protected $RoomType = null;

    /**
     * @param RoomType[] $RoomType
     */
    public function __construct(array $RoomType)
    {
      $this->RoomType = $RoomType;
    }

    /**
     * @return RoomType[]
     */
    public function getRoomType()
    {
      return $this->RoomType;
    }

    /**
     * @param RoomType[] $RoomType
     * @return \ContentHub\RoomTypes
     */
    public function setRoomType(array $RoomType)
    {
      $this->RoomType = $RoomType;
      return $this;
    }

}
