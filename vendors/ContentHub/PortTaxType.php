<?php

namespace ContentHub;

class PortTaxType
{

    /**
     * @var PriceType $Price
     */
    protected $Price = null;

    /**
     * @param PriceType $Price
     */
    public function __construct($Price)
    {
      $this->Price = $Price;
    }

    /**
     * @return PriceType
     */
    public function getPrice()
    {
      return $this->Price;
    }

    /**
     * @param PriceType $Price
     * @return \ContentHub\PortTaxType
     */
    public function setPrice($Price)
    {
      $this->Price = $Price;
      return $this;
    }

}
