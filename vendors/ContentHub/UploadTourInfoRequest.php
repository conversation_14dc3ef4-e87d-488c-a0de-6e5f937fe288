<?php

namespace ContentHub;

class UploadTourInfoRequest
{

    /**
     * @var string $securityKey
     */
    protected $securityKey = null;

    /**
     * @var string $fileName
     */
    protected $fileName = null;

    /**
     * @var base64Binary $fileData
     */
    protected $fileData = null;

    /**
     * @param string $securityKey
     * @param string $fileName
     * @param base64Binary $fileData
     */
    public function __construct($securityKey, $fileName, $fileData)
    {
      $this->securityKey = $securityKey;
      $this->fileName = $fileName;
      $this->fileData = $fileData;
    }

    /**
     * @return string
     */
    public function getSecurityKey()
    {
      return $this->securityKey;
    }

    /**
     * @param string $securityKey
     * @return \ContentHub\UploadTourInfoRequest
     */
    public function setSecurityKey($securityKey)
    {
      $this->securityKey = $securityKey;
      return $this;
    }

    /**
     * @return string
     */
    public function getFileName()
    {
      return $this->fileName;
    }

    /**
     * @param string $fileName
     * @return \ContentHub\UploadTourInfoRequest
     */
    public function setFileName($fileName)
    {
      $this->fileName = $fileName;
      return $this;
    }

    /**
     * @return base64Binary
     */
    public function getFileData()
    {
      return $this->fileData;
    }

    /**
     * @param base64Binary $fileData
     * @return \ContentHub\UploadTourInfoRequest
     */
    public function setFileData($fileData)
    {
      $this->fileData = $fileData;
      return $this;
    }

}
