<?php

namespace ContentHub;

class GetBrochureResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    /**
     * @var boolean $successful
     */
    protected $successful = null;

    /**
     * @var string $fileName
     */
    protected $fileName = null;

    /**
     * @var base64Binary $fileData
     */
    protected $fileData = null;

    /**
     * @param MessageContext $messageContext
     * @param boolean $successful
     * @param string $fileName
     * @param base64Binary $fileData
     */
    public function __construct($messageContext, $successful, $fileName, $fileData)
    {
      $this->messageContext = $messageContext;
      $this->successful = $successful;
      $this->fileName = $fileName;
      $this->fileData = $fileData;
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\GetBrochureResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getSuccessful()
    {
      return $this->successful;
    }

    /**
     * @param boolean $successful
     * @return \ContentHub\GetBrochureResponse
     */
    public function setSuccessful($successful)
    {
      $this->successful = $successful;
      return $this;
    }

    /**
     * @return string
     */
    public function getFileName()
    {
      return $this->fileName;
    }

    /**
     * @param string $fileName
     * @return \ContentHub\GetBrochureResponse
     */
    public function setFileName($fileName)
    {
      $this->fileName = $fileName;
      return $this;
    }

    /**
     * @return base64Binary
     */
    public function getFileData()
    {
      return $this->fileData;
    }

    /**
     * @param base64Binary $fileData
     * @return \ContentHub\GetBrochureResponse
     */
    public function setFileData($fileData)
    {
      $this->fileData = $fileData;
      return $this;
    }

}
