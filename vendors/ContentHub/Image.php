<?php

namespace ContentHub;

class Image
{

    /**
     * @var NMTOKEN $Name
     */
    protected $Name = null;

    /**
     * @var string $Caption
     */
    protected $Caption = null;

    /**
     * @var int $Width
     */
    protected $Width = null;

    /**
     * @var int $Height
     */
    protected $Height = null;

    /**
     * @var NMTOKEN $Type
     */
    protected $Type = null;

    /**
     * @var anyURI $Url
     */
    protected $Url = null;

    /**
     * @param NMTOKEN $Name
     * @param string $Caption
     * @param int $Width
     * @param int $Height
     * @param NMTOKEN $Type
     * @param anyURI $Url
     */
    public function __construct($Name, $Caption, $Width, $Height, $Type, $Url)
    {
      $this->Name = $Name;
      $this->Caption = $Caption;
      $this->Width = $Width;
      $this->Height = $Height;
      $this->Type = $Type;
      $this->Url = $Url;
    }

    /**
     * @return NMTOKEN
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param NMTOKEN $Name
     * @return \ContentHub\Image
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

    /**
     * @return string
     */
    public function getCaption()
    {
      return $this->Caption;
    }

    /**
     * @param string $Caption
     * @return \ContentHub\Image
     */
    public function setCaption($Caption)
    {
      $this->Caption = $Caption;
      return $this;
    }

    /**
     * @return int
     */
    public function getWidth()
    {
      return $this->Width;
    }

    /**
     * @param int $Width
     * @return \ContentHub\Image
     */
    public function setWidth($Width)
    {
      $this->Width = $Width;
      return $this;
    }

    /**
     * @return int
     */
    public function getHeight()
    {
      return $this->Height;
    }

    /**
     * @param int $Height
     * @return \ContentHub\Image
     */
    public function setHeight($Height)
    {
      $this->Height = $Height;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getType()
    {
      return $this->Type;
    }

    /**
     * @param NMTOKEN $Type
     * @return \ContentHub\Image
     */
    public function setType($Type)
    {
      $this->Type = $Type;
      return $this;
    }

    /**
     * @return anyURI
     */
    public function getUrl()
    {
      return $this->Url;
    }

    /**
     * @param anyURI $Url
     * @return \ContentHub\Image
     */
    public function setUrl($Url)
    {
      $this->Url = $Url;
      return $this;
    }

}
