<?php

namespace ContentHub;

class PriceType
{

    /**
     * @var float $Adult
     */
    protected $Adult = null;

    /**
     * @var float $Child
     */
    protected $Child = null;

    /**
     * @param float $Adult
     * @param float $Child
     */
    public function __construct($Adult, $Child)
    {
      $this->Adult = $Adult;
      $this->Child = $Child;
    }

    /**
     * @return float
     */
    public function getAdult()
    {
      return $this->Adult;
    }

    /**
     * @param float $Adult
     * @return \ContentHub\PriceType
     */
    public function setAdult($Adult)
    {
      $this->Adult = $Adult;
      return $this;
    }

    /**
     * @return float
     */
    public function getChild()
    {
      return $this->Child;
    }

    /**
     * @param float $Child
     * @return \ContentHub\PriceType
     */
    public function setChild($Child)
    {
      $this->Child = $Child;
      return $this;
    }

}
