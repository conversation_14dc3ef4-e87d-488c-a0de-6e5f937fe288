<?php

namespace ContentHub;

class Country
{

    /**
     * @var ISO3166CountryCode $Code
     */
    protected $Code = null;

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @var ContinentCode $ContinentCode
     */
    protected $ContinentCode = null;

    /**
     * @param ISO3166CountryCode $Code
     * @param string $Name
     * @param ContinentCode $ContinentCode
     */
    public function __construct($Code, $Name, $ContinentCode)
    {
      $this->Code = $Code;
      $this->Name = $Name;
      $this->ContinentCode = $ContinentCode;
    }

    /**
     * @return ISO3166CountryCode
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param ISO3166CountryCode $Code
     * @return \ContentHub\Country
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\Country
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

    /**
     * @return ContinentCode
     */
    public function getContinentCode()
    {
      return $this->ContinentCode;
    }

    /**
     * @param ContinentCode $ContinentCode
     * @return \ContentHub\Country
     */
    public function setContinentCode($ContinentCode)
    {
      $this->ContinentCode = $ContinentCode;
      return $this;
    }

}
