<?php

namespace ContentHub;

class MessageContext
{

    /**
     * @var Message[] $message
     */
    protected $message = null;

    /**
     * @var Status $status
     */
    protected $status = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return Message[]
     */
    public function getMessage()
    {
      return $this->message;
    }

    /**
     * @param Message[] $message
     * @return \ContentHub\MessageContext
     */
    public function setMessage(array $message = null)
    {
      $this->message = $message;
      return $this;
    }

    /**
     * @return Status
     */
    public function getStatus()
    {
      return $this->status;
    }

    /**
     * @param Status $status
     * @return \ContentHub\MessageContext
     */
    public function setStatus($status)
    {
      $this->status = $status;
      return $this;
    }

}
