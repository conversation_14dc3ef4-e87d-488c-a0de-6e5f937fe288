<?php

namespace ContentHub;

class GetTourCategoriesResponse
{

    /**
     * @var MessageContext $messageContext
     */
    protected $messageContext = null;

    /**
     * @var boolean $successful
     */
    protected $successful = null;

    /**
     * @var TourCategories $tourCategories
     */
    protected $tourCategories = null;

    /**
     * @param MessageContext $messageContext
     * @param boolean $successful
     * @param TourCategories $tourCategories
     */
    public function __construct($messageContext, $successful, $tourCategories)
    {
      $this->messageContext = $messageContext;
      $this->successful = $successful;
      $this->tourCategories = $tourCategories;
    }

    /**
     * @return MessageContext
     */
    public function getMessageContext()
    {
      return $this->messageContext;
    }

    /**
     * @param MessageContext $messageContext
     * @return \ContentHub\GetTourCategoriesResponse
     */
    public function setMessageContext($messageContext)
    {
      $this->messageContext = $messageContext;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getSuccessful()
    {
      return $this->successful;
    }

    /**
     * @param boolean $successful
     * @return \ContentHub\GetTourCategoriesResponse
     */
    public function setSuccessful($successful)
    {
      $this->successful = $successful;
      return $this;
    }

    /**
     * @return TourCategories
     */
    public function getTourCategories()
    {
      return $this->tourCategories;
    }

    /**
     * @param TourCategories $tourCategories
     * @return \ContentHub\GetTourCategoriesResponse
     */
    public function setTourCategories($tourCategories)
    {
      $this->tourCategories = $tourCategories;
      return $this;
    }

}
