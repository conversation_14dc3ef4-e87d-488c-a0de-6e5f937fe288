<?php

namespace ContentHub;

class SearchResults
{

    /**
     * @var string $tourCode
     */
    protected $tourCode = null;

    /**
     * @var string $tourName
     */
    protected $tourName = null;

    /**
     * @var string $sellingCompanyCode
     */
    protected $sellingCompanyCode = null;

    /**
     * @var float $priceFrom
     */
    protected $priceFrom = null;

    /**
     * @var float $priceTo
     */
    protected $priceTo = null;

    /**
     * @var string $brochureCode
     */
    protected $brochureCode = null;

    /**
     * @var string $brochureName
     */
    protected $brochureName = null;

    /**
     * @var MarketingFlags $marketingFlags
     */
    protected $marketingFlags = null;

    /**
     * @var Highlights $highlights
     */
    protected $highlights = null;

    /**
     * @var Assets $assets
     */
    protected $assets = null;

    /**
     * @var ContinentsVisited $continentsVisited
     */
    protected $continentsVisited = null;

    /**
     * @var CountriesVisited $countriesVisited
     */
    protected $countriesVisited = null;

    /**
     * @var boolean $earlyPaymentDiscountAvailable
     */
    protected $earlyPaymentDiscountAvailable = null;

    /**
     * @var boolean $definiteDeparturesAvailable
     */
    protected $definiteDeparturesAvailable = null;

    /**
     * @var int $duration
     */
    protected $duration = null;

    /**
     * @var string[] $accommodations
     */
    protected $accommodations = null;

    /**
     * @var string $startCity
     */
    protected $startCity = null;

    /**
     * @var string $airportsStartCity
     */
    protected $airportsStartCity = null;

    /**
     * @var string $endCity
     */
    protected $endCity = null;

    /**
     * @var string $airportsEndCity
     */
    protected $airportsEndCity = null;

    /**
     * @var RoomTypes $sellableRoomTypes
     */
    protected $sellableRoomTypes = null;

    /**
     * @var string $operatingProductCode
     */
    protected $operatingProductCode = null;

    /**
     * @var string $contractingSeason
     */
    protected $contractingSeason = null;

    /**
     * @var date $earliestDepartureStartDate
     */
    protected $earliestDepartureStartDate = null;

    /**
     * @var date $latestDepartureStartDate
     */
    protected $latestDepartureStartDate = null;

    /**
     * @var IncludedSubProducts $includedSubProducts
     */
    protected $includedSubProducts = null;

    /**
     * @var string $includedCruiseCabinType
     */
    protected $includedCruiseCabinType = null;

    /**
     * @var AdditionalDefiners $additionalDefiners
     */
    protected $additionalDefiners = null;

    /**
     * @param string $tourCode
     * @param string $tourName
     * @param string $sellingCompanyCode
     * @param float $priceFrom
     * @param float $priceTo
     * @param string $brochureCode
     * @param string $brochureName
     * @param ContinentsVisited $continentsVisited
     * @param CountriesVisited $countriesVisited
     * @param boolean $earlyPaymentDiscountAvailable
     * @param boolean $definiteDeparturesAvailable
     * @param int $duration
     * @param string $startCity
     * @param string $endCity
     * @param RoomTypes $sellableRoomTypes
     * @param string $operatingProductCode
     * @param string $contractingSeason
     * @param date $earliestDepartureStartDate
     * @param date $latestDepartureStartDate
     */
    public function __construct($tourCode, $tourName, $sellingCompanyCode, $priceFrom, $priceTo, $brochureCode, $brochureName, $continentsVisited, $countriesVisited, $earlyPaymentDiscountAvailable, $definiteDeparturesAvailable, $duration, $startCity, $endCity, $sellableRoomTypes, $operatingProductCode, $contractingSeason, $earliestDepartureStartDate, $latestDepartureStartDate)
    {
      $this->tourCode = $tourCode;
      $this->tourName = $tourName;
      $this->sellingCompanyCode = $sellingCompanyCode;
      $this->priceFrom = $priceFrom;
      $this->priceTo = $priceTo;
      $this->brochureCode = $brochureCode;
      $this->brochureName = $brochureName;
      $this->continentsVisited = $continentsVisited;
      $this->countriesVisited = $countriesVisited;
      $this->earlyPaymentDiscountAvailable = $earlyPaymentDiscountAvailable;
      $this->definiteDeparturesAvailable = $definiteDeparturesAvailable;
      $this->duration = $duration;
      $this->startCity = $startCity;
      $this->endCity = $endCity;
      $this->sellableRoomTypes = $sellableRoomTypes;
      $this->operatingProductCode = $operatingProductCode;
      $this->contractingSeason = $contractingSeason;
      $this->earliestDepartureStartDate = $earliestDepartureStartDate;
      $this->latestDepartureStartDate = $latestDepartureStartDate;
    }

    /**
     * @return string
     */
    public function getTourCode()
    {
      return $this->tourCode;
    }

    /**
     * @param string $tourCode
     * @return \ContentHub\SearchResults
     */
    public function setTourCode($tourCode)
    {
      $this->tourCode = $tourCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getTourName()
    {
      return $this->tourName;
    }

    /**
     * @param string $tourName
     * @return \ContentHub\SearchResults
     */
    public function setTourName($tourName)
    {
      $this->tourName = $tourName;
      return $this;
    }

    /**
     * @return string
     */
    public function getSellingCompanyCode()
    {
      return $this->sellingCompanyCode;
    }

    /**
     * @param string $sellingCompanyCode
     * @return \ContentHub\SearchResults
     */
    public function setSellingCompanyCode($sellingCompanyCode)
    {
      $this->sellingCompanyCode = $sellingCompanyCode;
      return $this;
    }

    /**
     * @return float
     */
    public function getPriceFrom()
    {
      return $this->priceFrom;
    }

    /**
     * @param float $priceFrom
     * @return \ContentHub\SearchResults
     */
    public function setPriceFrom($priceFrom)
    {
      $this->priceFrom = $priceFrom;
      return $this;
    }

    /**
     * @return float
     */
    public function getPriceTo()
    {
      return $this->priceTo;
    }

    /**
     * @param float $priceTo
     * @return \ContentHub\SearchResults
     */
    public function setPriceTo($priceTo)
    {
      $this->priceTo = $priceTo;
      return $this;
    }

    /**
     * @return string
     */
    public function getBrochureCode()
    {
      return $this->brochureCode;
    }

    /**
     * @param string $brochureCode
     * @return \ContentHub\SearchResults
     */
    public function setBrochureCode($brochureCode)
    {
      $this->brochureCode = $brochureCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getBrochureName()
    {
      return $this->brochureName;
    }

    /**
     * @param string $brochureName
     * @return \ContentHub\SearchResults
     */
    public function setBrochureName($brochureName)
    {
      $this->brochureName = $brochureName;
      return $this;
    }

    /**
     * @return MarketingFlags
     */
    public function getMarketingFlags()
    {
      return $this->marketingFlags;
    }

    /**
     * @param MarketingFlags $marketingFlags
     * @return \ContentHub\SearchResults
     */
    public function setMarketingFlags($marketingFlags)
    {
      $this->marketingFlags = $marketingFlags;
      return $this;
    }

    /**
     * @return Highlights
     */
    public function getHighlights()
    {
      return $this->highlights;
    }

    /**
     * @param Highlights $highlights
     * @return \ContentHub\SearchResults
     */
    public function setHighlights($highlights)
    {
      $this->highlights = $highlights;
      return $this;
    }

    /**
     * @return Assets
     */
    public function getAssets()
    {
      return $this->assets;
    }

    /**
     * @param Assets $assets
     * @return \ContentHub\SearchResults
     */
    public function setAssets($assets)
    {
      $this->assets = $assets;
      return $this;
    }

    /**
     * @return ContinentsVisited
     */
    public function getContinentsVisited()
    {
      return $this->continentsVisited;
    }

    /**
     * @param ContinentsVisited $continentsVisited
     * @return \ContentHub\SearchResults
     */
    public function setContinentsVisited($continentsVisited)
    {
      $this->continentsVisited = $continentsVisited;
      return $this;
    }

    /**
     * @return CountriesVisited
     */
    public function getCountriesVisited()
    {
      return $this->countriesVisited;
    }

    /**
     * @param CountriesVisited $countriesVisited
     * @return \ContentHub\SearchResults
     */
    public function setCountriesVisited($countriesVisited)
    {
      $this->countriesVisited = $countriesVisited;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getEarlyPaymentDiscountAvailable()
    {
      return $this->earlyPaymentDiscountAvailable;
    }

    /**
     * @param boolean $earlyPaymentDiscountAvailable
     * @return \ContentHub\SearchResults
     */
    public function setEarlyPaymentDiscountAvailable($earlyPaymentDiscountAvailable)
    {
      $this->earlyPaymentDiscountAvailable = $earlyPaymentDiscountAvailable;
      return $this;
    }

    /**
     * @return boolean
     */
    public function getDefiniteDeparturesAvailable()
    {
      return $this->definiteDeparturesAvailable;
    }

    /**
     * @param boolean $definiteDeparturesAvailable
     * @return \ContentHub\SearchResults
     */
    public function setDefiniteDeparturesAvailable($definiteDeparturesAvailable)
    {
      $this->definiteDeparturesAvailable = $definiteDeparturesAvailable;
      return $this;
    }

    /**
     * @return int
     */
    public function getDuration()
    {
      return $this->duration;
    }

    /**
     * @param int $duration
     * @return \ContentHub\SearchResults
     */
    public function setDuration($duration)
    {
      $this->duration = $duration;
      return $this;
    }

    /**
     * @return string[]
     */
    public function getAccommodations()
    {
      return $this->accommodations;
    }

    /**
     * @param string[] $accommodations
     * @return \ContentHub\SearchResults
     */
    public function setAccommodations(array $accommodations = null)
    {
      $this->accommodations = $accommodations;
      return $this;
    }

    /**
     * @return string
     */
    public function getStartCity()
    {
      return $this->startCity;
    }

    /**
     * @param string $startCity
     * @return \ContentHub\SearchResults
     */
    public function setStartCity($startCity)
    {
      $this->startCity = $startCity;
      return $this;
    }

    /**
     * @return string
     */
    public function getAirportsStartCity()
    {
      return $this->airportsStartCity;
    }

    /**
     * @param string $airportsStartCity
     * @return \ContentHub\SearchResults
     */
    public function setAirportsStartCity($airportsStartCity)
    {
      $this->airportsStartCity = $airportsStartCity;
      return $this;
    }

    /**
     * @return string
     */
    public function getEndCity()
    {
      return $this->endCity;
    }

    /**
     * @param string $endCity
     * @return \ContentHub\SearchResults
     */
    public function setEndCity($endCity)
    {
      $this->endCity = $endCity;
      return $this;
    }

    /**
     * @return string
     */
    public function getAirportsEndCity()
    {
      return $this->airportsEndCity;
    }

    /**
     * @param string $airportsEndCity
     * @return \ContentHub\SearchResults
     */
    public function setAirportsEndCity($airportsEndCity)
    {
      $this->airportsEndCity = $airportsEndCity;
      return $this;
    }

    /**
     * @return RoomTypes
     */
    public function getSellableRoomTypes()
    {
      return $this->sellableRoomTypes;
    }

    /**
     * @param RoomTypes $sellableRoomTypes
     * @return \ContentHub\SearchResults
     */
    public function setSellableRoomTypes($sellableRoomTypes)
    {
      $this->sellableRoomTypes = $sellableRoomTypes;
      return $this;
    }

    /**
     * @return string
     */
    public function getOperatingProductCode()
    {
      return $this->operatingProductCode;
    }

    /**
     * @param string $operatingProductCode
     * @return \ContentHub\SearchResults
     */
    public function setOperatingProductCode($operatingProductCode)
    {
      $this->operatingProductCode = $operatingProductCode;
      return $this;
    }

    /**
     * @return string
     */
    public function getContractingSeason()
    {
      return $this->contractingSeason;
    }

    /**
     * @param string $contractingSeason
     * @return \ContentHub\SearchResults
     */
    public function setContractingSeason($contractingSeason)
    {
      $this->contractingSeason = $contractingSeason;
      return $this;
    }

    /**
     * @return date
     */
    public function getEarliestDepartureStartDate()
    {
      return $this->earliestDepartureStartDate;
    }

    /**
     * @param date $earliestDepartureStartDate
     * @return \ContentHub\SearchResults
     */
    public function setEarliestDepartureStartDate($earliestDepartureStartDate)
    {
      $this->earliestDepartureStartDate = $earliestDepartureStartDate;
      return $this;
    }

    /**
     * @return date
     */
    public function getLatestDepartureStartDate()
    {
      return $this->latestDepartureStartDate;
    }

    /**
     * @param date $latestDepartureStartDate
     * @return \ContentHub\SearchResults
     */
    public function setLatestDepartureStartDate($latestDepartureStartDate)
    {
      $this->latestDepartureStartDate = $latestDepartureStartDate;
      return $this;
    }

    /**
     * @return IncludedSubProducts
     */
    public function getIncludedSubProducts()
    {
      return $this->includedSubProducts;
    }

    /**
     * @param IncludedSubProducts $includedSubProducts
     * @return \ContentHub\SearchResults
     */
    public function setIncludedSubProducts($includedSubProducts)
    {
      $this->includedSubProducts = $includedSubProducts;
      return $this;
    }

    /**
     * @return string
     */
    public function getIncludedCruiseCabinType()
    {
      return $this->includedCruiseCabinType;
    }

    /**
     * @param string $includedCruiseCabinType
     * @return \ContentHub\SearchResults
     */
    public function setIncludedCruiseCabinType($includedCruiseCabinType)
    {
      $this->includedCruiseCabinType = $includedCruiseCabinType;
      return $this;
    }

    /**
     * @return AdditionalDefiners
     */
    public function getAdditionalDefiners()
    {
      return $this->additionalDefiners;
    }

    /**
     * @param AdditionalDefiners $additionalDefiners
     * @return \ContentHub\SearchResults
     */
    public function setAdditionalDefiners($additionalDefiners)
    {
      $this->additionalDefiners = $additionalDefiners;
      return $this;
    }

}
