<?php

namespace ContentHub;

class RoomType
{

    /**
     * @var OccupancyRuleType $OccupancyRule
     */
    protected $OccupancyRule = null;

    /**
     * @var RoomPriceType $Price
     */
    protected $Price = null;

    /**
     * @var RoomTypeCodeType $Type
     */
    protected $Type = null;

    /**
     * @param OccupancyRuleType $OccupancyRule
     * @param RoomPriceType $Price
     * @param RoomTypeCodeType $Type
     */
    public function __construct($OccupancyRule, $Price, $Type)
    {
      $this->OccupancyRule = $OccupancyRule;
      $this->Price = $Price;
      $this->Type = $Type;
    }

    /**
     * @return OccupancyRuleType
     */
    public function getOccupancyRule()
    {
      return $this->OccupancyRule;
    }

    /**
     * @param OccupancyRuleType $OccupancyRule
     * @return \ContentHub\RoomType
     */
    public function setOccupancyRule($OccupancyRule)
    {
      $this->OccupancyRule = $OccupancyRule;
      return $this;
    }

    /**
     * @return RoomPriceType
     */
    public function getPrice()
    {
      return $this->Price;
    }

    /**
     * @param RoomPriceType $Price
     * @return \ContentHub\RoomType
     */
    public function setPrice($Price)
    {
      $this->Price = $Price;
      return $this;
    }

    /**
     * @return RoomTypeCodeType
     */
    public function getType()
    {
      return $this->Type;
    }

    /**
     * @param RoomTypeCodeType $Type
     * @return \ContentHub\RoomType
     */
    public function setType($Type)
    {
      $this->Type = $Type;
      return $this;
    }

}
