<?php

namespace ContentHub;

class CataloguedTour
{

    /**
     * @var NMTOKEN $Code
     */
    protected $Code = null;

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @param NMTOKEN $Code
     * @param string $Name
     */
    public function __construct($Code, $Name)
    {
      $this->Code = $Code;
      $this->Name = $Name;
    }

    /**
     * @return NMTOKEN
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param NMTOKEN $Code
     * @return \ContentHub\CataloguedTour
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\CataloguedTour
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

}
