<?php

namespace ContentHub;

class AssociatedProductsType
{

    /**
     * @var AccommodationProductsType $AccommodationProducts
     */
    protected $AccommodationProducts = null;

    /**
     * @var MiscellaneousProductsType $MiscellaneousProducts
     */
    protected $MiscellaneousProducts = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return AccommodationProductsType
     */
    public function getAccommodationProducts()
    {
      return $this->AccommodationProducts;
    }

    /**
     * @param AccommodationProductsType $AccommodationProducts
     * @return \ContentHub\AssociatedProductsType
     */
    public function setAccommodationProducts($AccommodationProducts)
    {
      $this->AccommodationProducts = $AccommodationProducts;
      return $this;
    }

    /**
     * @return MiscellaneousProductsType
     */
    public function getMiscellaneousProducts()
    {
      return $this->MiscellaneousProducts;
    }

    /**
     * @param MiscellaneousProductsType $MiscellaneousProducts
     * @return \ContentHub\AssociatedProductsType
     */
    public function setMiscellaneousProducts($MiscellaneousProducts)
    {
      $this->MiscellaneousProducts = $MiscellaneousProducts;
      return $this;
    }

}
