<?php

namespace ContentHub;

class PassengerType
{

    /**
     * @var PassengerTypeType $Type
     */
    protected $Type = null;

    /**
     * @var int $AgeFrom
     */
    protected $AgeFrom = null;

    /**
     * @var int $AgeTo
     */
    protected $AgeTo = null;

    /**
     * @param PassengerTypeType $Type
     * @param int $AgeFrom
     * @param int $AgeTo
     */
    public function __construct($Type, $AgeFrom, $AgeTo)
    {
      $this->Type = $Type;
      $this->AgeFrom = $AgeFrom;
      $this->AgeTo = $AgeTo;
    }

    /**
     * @return PassengerTypeType
     */
    public function getType()
    {
      return $this->Type;
    }

    /**
     * @param PassengerTypeType $Type
     * @return \ContentHub\PassengerType
     */
    public function setType($Type)
    {
      $this->Type = $Type;
      return $this;
    }

    /**
     * @return int
     */
    public function getAgeFrom()
    {
      return $this->AgeFrom;
    }

    /**
     * @param int $AgeFrom
     * @return \ContentHub\PassengerType
     */
    public function setAgeFrom($AgeFrom)
    {
      $this->AgeFrom = $AgeFrom;
      return $this;
    }

    /**
     * @return int
     */
    public function getAgeTo()
    {
      return $this->AgeTo;
    }

    /**
     * @param int $AgeTo
     * @return \ContentHub\PassengerType
     */
    public function setAgeTo($AgeTo)
    {
      $this->AgeTo = $AgeTo;
      return $this;
    }

}
