<?php

namespace ContentHub;

class UploadFileStatusType
{

    /**
     * @var string $fileName
     */
    protected $fileName = null;

    /**
     * @var string $fileStatus
     */
    protected $fileStatus = null;

    /**
     * @param string $fileName
     * @param string $fileStatus
     */
    public function __construct($fileName, $fileStatus)
    {
      $this->fileName = $fileName;
      $this->fileStatus = $fileStatus;
    }

    /**
     * @return string
     */
    public function getFileName()
    {
      return $this->fileName;
    }

    /**
     * @param string $fileName
     * @return \ContentHub\UploadFileStatusType
     */
    public function setFileName($fileName)
    {
      $this->fileName = $fileName;
      return $this;
    }

    /**
     * @return string
     */
    public function getFileStatus()
    {
      return $this->fileStatus;
    }

    /**
     * @param string $fileStatus
     * @return \ContentHub\UploadFileStatusType
     */
    public function setFileStatus($fileStatus)
    {
      $this->fileStatus = $fileStatus;
      return $this;
    }

}
