<?php

namespace ContentHub;

class SellingCompany
{

    /**
     * @var Brochure[] $Brochure
     */
    protected $Brochure = null;

    /**
     * @var anyURI $TermsAndConditionsLinkURL
     */
    protected $TermsAndConditionsLinkURL = null;

    /**
     * @var TourCategories $TourCategories
     */
    protected $TourCategories = null;

    /**
     * @var MarketingFlags $MarketingFlags
     */
    protected $MarketingFlags = null;

    /**
     * @var NMTOKEN $CurrencyCode
     */
    protected $CurrencyCode = null;

    /**
     * @var NMTOKEN $Code
     */
    protected $Code = null;

    /**
     * @param Brochure[] $Brochure
     * @param TourCategories $TourCategories
     * @param NMTOKEN $CurrencyCode
     * @param NMTOKEN $Code
     */
    public function __construct(array $Brochure, $TourCategories, $CurrencyCode, $Code)
    {
      $this->Brochure = $Brochure;
      $this->TourCategories = $TourCategories;
      $this->CurrencyCode = $CurrencyCode;
      $this->Code = $Code;
    }

    /**
     * @return Brochure[]
     */
    public function getBrochure()
    {
      return $this->Brochure;
    }

    /**
     * @param Brochure[] $Brochure
     * @return \ContentHub\SellingCompany
     */
    public function setBrochure(array $Brochure)
    {
      $this->Brochure = $Brochure;
      return $this;
    }

    /**
     * @return anyURI
     */
    public function getTermsAndConditionsLinkURL()
    {
      return $this->TermsAndConditionsLinkURL;
    }

    /**
     * @param anyURI $TermsAndConditionsLinkURL
     * @return \ContentHub\SellingCompany
     */
    public function setTermsAndConditionsLinkURL($TermsAndConditionsLinkURL)
    {
      $this->TermsAndConditionsLinkURL = $TermsAndConditionsLinkURL;
      return $this;
    }

    /**
     * @return TourCategories
     */
    public function getTourCategories()
    {
      return $this->TourCategories;
    }

    /**
     * @param TourCategories $TourCategories
     * @return \ContentHub\SellingCompany
     */
    public function setTourCategories($TourCategories)
    {
      $this->TourCategories = $TourCategories;
      return $this;
    }

    /**
     * @return MarketingFlags
     */
    public function getMarketingFlags()
    {
      return $this->MarketingFlags;
    }

    /**
     * @param MarketingFlags $MarketingFlags
     * @return \ContentHub\SellingCompany
     */
    public function setMarketingFlags($MarketingFlags)
    {
      $this->MarketingFlags = $MarketingFlags;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getCurrencyCode()
    {
      return $this->CurrencyCode;
    }

    /**
     * @param NMTOKEN $CurrencyCode
     * @return \ContentHub\SellingCompany
     */
    public function setCurrencyCode($CurrencyCode)
    {
      $this->CurrencyCode = $CurrencyCode;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getCode()
    {
      return $this->Code;
    }

    /**
     * @param NMTOKEN $Code
     * @return \ContentHub\SellingCompany
     */
    public function setCode($Code)
    {
      $this->Code = $Code;
      return $this;
    }

}
