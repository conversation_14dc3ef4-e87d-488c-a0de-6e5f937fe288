<?php

namespace ContentHub;

class MessageType
{

    /**
     * @var string $code
     */
    protected $code = null;

    /**
     * @var string $description
     */
    protected $description = null;

    /**
     * @var Severity $severity
     */
    protected $severity = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getCode()
    {
      return $this->code;
    }

    /**
     * @param string $code
     * @return \ContentHub\MessageType
     */
    public function setCode($code)
    {
      $this->code = $code;
      return $this;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
      return $this->description;
    }

    /**
     * @param string $description
     * @return \ContentHub\MessageType
     */
    public function setDescription($description)
    {
      $this->description = $description;
      return $this;
    }

    /**
     * @return Severity
     */
    public function getSeverity()
    {
      return $this->severity;
    }

    /**
     * @param Severity $severity
     * @return \ContentHub\MessageType
     */
    public function setSeverity($severity)
    {
      $this->severity = $severity;
      return $this;
    }

}
