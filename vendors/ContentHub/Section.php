<?php

namespace ContentHub;

class Section
{

    /**
     * @var string $Title
     */
    protected $Title = null;

    /**
     * @var Text $Text
     */
    protected $Text = null;

    /**
     * @param Text $Text
     */
    public function __construct($Text)
    {
      $this->Text = $Text;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
      return $this->Title;
    }

    /**
     * @param string $Title
     * @return \ContentHub\Section
     */
    public function setTitle($Title)
    {
      $this->Title = $Title;
      return $this;
    }

    /**
     * @return Text
     */
    public function getText()
    {
      return $this->Text;
    }

    /**
     * @param Text $Text
     * @return \ContentHub\Section
     */
    public function setText($Text)
    {
      $this->Text = $Text;
      return $this;
    }

}
