<?php

namespace ContentHub;

class OptionalExtras
{

    /**
     * @var Extra[] $Extra
     */
    protected $Extra = null;

    /**
     * @param Extra[] $Extra
     */
    public function __construct(array $Extra)
    {
      $this->Extra = $Extra;
    }

    /**
     * @return Extra[]
     */
    public function getExtra()
    {
      return $this->Extra;
    }

    /**
     * @param Extra[] $Extra
     * @return \ContentHub\OptionalExtras
     */
    public function setExtra(array $Extra)
    {
      $this->Extra = $Extra;
      return $this;
    }

}
