<?php

namespace ContentHub;

class TourInfo
{

    /**
     * @var string $TourName
     */
    protected $TourName = null;

    /**
     * @var CataloguedTour $CataloguedTour
     */
    protected $CataloguedTour = null;

    /**
     * @var SellingCompanies $SellingCompanies
     */
    protected $SellingCompanies = null;

    /**
     * @var TourVariationDefiners $TourVariationDefiners
     */
    protected $TourVariationDefiners = null;

    /**
     * @var ContinentsVisited $ContinentsVisited
     */
    protected $ContinentsVisited = null;

    /**
     * @var CountriesVisited $CountriesVisited
     */
    protected $CountriesVisited = null;

    /**
     * @var LocationsVisited $LocationsVisited
     */
    protected $LocationsVisited = null;

    /**
     * @var string $Description
     */
    protected $Description = null;

    /**
     * @var Assets $Assets
     */
    protected $Assets = null;

    /**
     * @var Itinerary $Itinerary
     */
    protected $Itinerary = null;

    /**
     * @var WhatsIncluded $WhatsIncluded
     */
    protected $WhatsIncluded = null;

    /**
     * @var Highlights $Highlights
     */
    protected $Highlights = null;

    /**
     * @var AirportTransfers $AirportTransfers
     */
    protected $AirportTransfers = null;

    /**
     * @var Notes $Notes
     */
    protected $Notes = null;

    /**
     * @var AdditionalInfo $AdditionalInfo
     */
    protected $AdditionalInfo = null;

    /**
     * @var NMTOKEN $BrandCode
     */
    protected $BrandCode = null;

    /**
     * @var NMTOKEN $CMSId
     */
    protected $CMSId = null;

    /**
     * @var NMTOKEN $CMSTourId
     */
    protected $CMSTourId = null;

    /**
     * @var int $Duration
     */
    protected $Duration = null;

    /**
     * @var NMTOKEN $TourCode
     */
    protected $TourCode = null;

    /**
     * @param string $TourName
     * @param CataloguedTour $CataloguedTour
     * @param SellingCompanies $SellingCompanies
     * @param TourVariationDefiners $TourVariationDefiners
     * @param ContinentsVisited $ContinentsVisited
     * @param CountriesVisited $CountriesVisited
     * @param LocationsVisited $LocationsVisited
     * @param string $Description
     * @param Assets $Assets
     * @param Itinerary $Itinerary
     * @param WhatsIncluded $WhatsIncluded
     * @param Highlights $Highlights
     * @param AirportTransfers $AirportTransfers
     * @param Notes $Notes
     * @param AdditionalInfo $AdditionalInfo
     * @param NMTOKEN $BrandCode
     * @param NMTOKEN $CMSId
     * @param NMTOKEN $CMSTourId
     * @param int $Duration
     * @param NMTOKEN $TourCode
     */
    public function __construct($TourName, $CataloguedTour, $SellingCompanies, $TourVariationDefiners, $ContinentsVisited, $CountriesVisited, $LocationsVisited, $Description, $Assets, $Itinerary, $WhatsIncluded, $Highlights, $AirportTransfers, $Notes, $AdditionalInfo, $BrandCode, $CMSId, $CMSTourId, $Duration, $TourCode)
    {
      $this->TourName = $TourName;
      $this->CataloguedTour = $CataloguedTour;
      $this->SellingCompanies = $SellingCompanies;
      $this->TourVariationDefiners = $TourVariationDefiners;
      $this->ContinentsVisited = $ContinentsVisited;
      $this->CountriesVisited = $CountriesVisited;
      $this->LocationsVisited = $LocationsVisited;
      $this->Description = $Description;
      $this->Assets = $Assets;
      $this->Itinerary = $Itinerary;
      $this->WhatsIncluded = $WhatsIncluded;
      $this->Highlights = $Highlights;
      $this->AirportTransfers = $AirportTransfers;
      $this->Notes = $Notes;
      $this->AdditionalInfo = $AdditionalInfo;
      $this->BrandCode = $BrandCode;
      $this->CMSId = $CMSId;
      $this->CMSTourId = $CMSTourId;
      $this->Duration = $Duration;
      $this->TourCode = $TourCode;
    }

    /**
     * @return string
     */
    public function getTourName()
    {
      return $this->TourName;
    }

    /**
     * @param string $TourName
     * @return \ContentHub\TourInfo
     */
    public function setTourName($TourName)
    {
      $this->TourName = $TourName;
      return $this;
    }

    /**
     * @return CataloguedTour
     */
    public function getCataloguedTour()
    {
      return $this->CataloguedTour;
    }

    /**
     * @param CataloguedTour $CataloguedTour
     * @return \ContentHub\TourInfo
     */
    public function setCataloguedTour($CataloguedTour)
    {
      $this->CataloguedTour = $CataloguedTour;
      return $this;
    }

    /**
     * @return SellingCompanies
     */
    public function getSellingCompanies()
    {
      return $this->SellingCompanies;
    }

    /**
     * @param SellingCompanies $SellingCompanies
     * @return \ContentHub\TourInfo
     */
    public function setSellingCompanies($SellingCompanies)
    {
      $this->SellingCompanies = $SellingCompanies;
      return $this;
    }

    /**
     * @return TourVariationDefiners
     */
    public function getTourVariationDefiners()
    {
      return $this->TourVariationDefiners;
    }

    /**
     * @param TourVariationDefiners $TourVariationDefiners
     * @return \ContentHub\TourInfo
     */
    public function setTourVariationDefiners($TourVariationDefiners)
    {
      $this->TourVariationDefiners = $TourVariationDefiners;
      return $this;
    }

    /**
     * @return ContinentsVisited
     */
    public function getContinentsVisited()
    {
      return $this->ContinentsVisited;
    }

    /**
     * @param ContinentsVisited $ContinentsVisited
     * @return \ContentHub\TourInfo
     */
    public function setContinentsVisited($ContinentsVisited)
    {
      $this->ContinentsVisited = $ContinentsVisited;
      return $this;
    }

    /**
     * @return CountriesVisited
     */
    public function getCountriesVisited()
    {
      return $this->CountriesVisited;
    }

    /**
     * @param CountriesVisited $CountriesVisited
     * @return \ContentHub\TourInfo
     */
    public function setCountriesVisited($CountriesVisited)
    {
      $this->CountriesVisited = $CountriesVisited;
      return $this;
    }

    /**
     * @return LocationsVisited
     */
    public function getLocationsVisited()
    {
      return $this->LocationsVisited;
    }

    /**
     * @param LocationsVisited $LocationsVisited
     * @return \ContentHub\TourInfo
     */
    public function setLocationsVisited($LocationsVisited)
    {
      $this->LocationsVisited = $LocationsVisited;
      return $this;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
      return $this->Description;
    }

    /**
     * @param string $Description
     * @return \ContentHub\TourInfo
     */
    public function setDescription($Description)
    {
      $this->Description = $Description;
      return $this;
    }

    /**
     * @return Assets
     */
    public function getAssets()
    {
      return $this->Assets;
    }

    /**
     * @param Assets $Assets
     * @return \ContentHub\TourInfo
     */
    public function setAssets($Assets)
    {
      $this->Assets = $Assets;
      return $this;
    }

    /**
     * @return Itinerary
     */
    public function getItinerary()
    {
      return $this->Itinerary;
    }

    /**
     * @param Itinerary $Itinerary
     * @return \ContentHub\TourInfo
     */
    public function setItinerary($Itinerary)
    {
      $this->Itinerary = $Itinerary;
      return $this;
    }

    /**
     * @return WhatsIncluded
     */
    public function getWhatsIncluded()
    {
      return $this->WhatsIncluded;
    }

    /**
     * @param WhatsIncluded $WhatsIncluded
     * @return \ContentHub\TourInfo
     */
    public function setWhatsIncluded($WhatsIncluded)
    {
      $this->WhatsIncluded = $WhatsIncluded;
      return $this;
    }

    /**
     * @return Highlights
     */
    public function getHighlights()
    {
      return $this->Highlights;
    }

    /**
     * @param Highlights $Highlights
     * @return \ContentHub\TourInfo
     */
    public function setHighlights($Highlights)
    {
      $this->Highlights = $Highlights;
      return $this;
    }

    /**
     * @return AirportTransfers
     */
    public function getAirportTransfers()
    {
      return $this->AirportTransfers;
    }

    /**
     * @param AirportTransfers $AirportTransfers
     * @return \ContentHub\TourInfo
     */
    public function setAirportTransfers($AirportTransfers)
    {
      $this->AirportTransfers = $AirportTransfers;
      return $this;
    }

    /**
     * @return Notes
     */
    public function getNotes()
    {
      return $this->Notes;
    }

    /**
     * @param Notes $Notes
     * @return \ContentHub\TourInfo
     */
    public function setNotes($Notes)
    {
      $this->Notes = $Notes;
      return $this;
    }

    /**
     * @return AdditionalInfo
     */
    public function getAdditionalInfo()
    {
      return $this->AdditionalInfo;
    }

    /**
     * @param AdditionalInfo $AdditionalInfo
     * @return \ContentHub\TourInfo
     */
    public function setAdditionalInfo($AdditionalInfo)
    {
      $this->AdditionalInfo = $AdditionalInfo;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getBrandCode()
    {
      return $this->BrandCode;
    }

    /**
     * @param NMTOKEN $BrandCode
     * @return \ContentHub\TourInfo
     */
    public function setBrandCode($BrandCode)
    {
      $this->BrandCode = $BrandCode;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getCMSId()
    {
      return $this->CMSId;
    }

    /**
     * @param NMTOKEN $CMSId
     * @return \ContentHub\TourInfo
     */
    public function setCMSId($CMSId)
    {
      $this->CMSId = $CMSId;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getCMSTourId()
    {
      return $this->CMSTourId;
    }

    /**
     * @param NMTOKEN $CMSTourId
     * @return \ContentHub\TourInfo
     */
    public function setCMSTourId($CMSTourId)
    {
      $this->CMSTourId = $CMSTourId;
      return $this;
    }

    /**
     * @return int
     */
    public function getDuration()
    {
      return $this->Duration;
    }

    /**
     * @param int $Duration
     * @return \ContentHub\TourInfo
     */
    public function setDuration($Duration)
    {
      $this->Duration = $Duration;
      return $this;
    }

    /**
     * @return NMTOKEN
     */
    public function getTourCode()
    {
      return $this->TourCode;
    }

    /**
     * @param NMTOKEN $TourCode
     * @return \ContentHub\TourInfo
     */
    public function setTourCode($TourCode)
    {
      $this->TourCode = $TourCode;
      return $this;
    }

}
