<?php

namespace ContentHub;

class CurrentBrandStatusType
{

    /**
     * @var SnapshotType $snapshot
     */
    protected $snapshot = null;

    /**
     * @var string $brandStatus
     */
    protected $brandStatus = null;

    /**
     * @param string $brandStatus
     */
    public function __construct($brandStatus)
    {
      $this->brandStatus = $brandStatus;
    }

    /**
     * @return SnapshotType
     */
    public function getSnapshot()
    {
      return $this->snapshot;
    }

    /**
     * @param SnapshotType $snapshot
     * @return \ContentHub\CurrentBrandStatusType
     */
    public function setSnapshot($snapshot)
    {
      $this->snapshot = $snapshot;
      return $this;
    }

    /**
     * @return string
     */
    public function getBrandStatus()
    {
      return $this->brandStatus;
    }

    /**
     * @param string $brandStatus
     * @return \ContentHub\CurrentBrandStatusType
     */
    public function setBrandStatus($brandStatus)
    {
      $this->brandStatus = $brandStatus;
      return $this;
    }

}
