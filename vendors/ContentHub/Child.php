<?php

namespace ContentHub;

class Child
{

    /**
     * @var float $Base
     */
    protected $Base = null;

    /**
     * @var float $Combined
     */
    protected $Combined = null;

    /**
     * @param float $Base
     * @param float $Combined
     */
    public function __construct($Base, $Combined)
    {
      $this->Base = $Base;
      $this->Combined = $Combined;
    }

    /**
     * @return float
     */
    public function getBase()
    {
      return $this->Base;
    }

    /**
     * @param float $Base
     * @return \ContentHub\Child
     */
    public function setBase($Base)
    {
      $this->Base = $Base;
      return $this;
    }

    /**
     * @return float
     */
    public function getCombined()
    {
      return $this->Combined;
    }

    /**
     * @param float $Combined
     * @return \ContentHub\Child
     */
    public function setCombined($Combined)
    {
      $this->Combined = $Combined;
      return $this;
    }

}
