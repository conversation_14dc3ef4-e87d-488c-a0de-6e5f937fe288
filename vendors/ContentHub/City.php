<?php

namespace ContentHub;

class City
{

    /**
     * @var string $Name
     */
    protected $Name = null;

    /**
     * @var Airports $Airports
     */
    protected $Airports = null;

    /**
     * @param string $Name
     */
    public function __construct($Name)
    {
      $this->Name = $Name;
    }

    /**
     * @return string
     */
    public function getName()
    {
      return $this->Name;
    }

    /**
     * @param string $Name
     * @return \ContentHub\City
     */
    public function setName($Name)
    {
      $this->Name = $Name;
      return $this;
    }

    /**
     * @return Airports
     */
    public function getAirports()
    {
      return $this->Airports;
    }

    /**
     * @param Airports $Airports
     * @return \ContentHub\City
     */
    public function setAirports($Airports)
    {
      $this->Airports = $Airports;
      return $this;
    }

}
