<div id="<?php echo $pluralVar;?>" class="<?php echo $action; ?>">
  <div class="title clearfix">
    <h2><?php echo "<?php __('".Inflector::humanize($action)." {$singularHumanName}');?>";?></h2>
    <?php echo "<?php echo \$this->element('webadmin_actions'); ?>\n"; ?>
  </div>
<?php
echo "\t<?php \$webAdmin->sessionFlash(); ?>\n";
if ($isTree) {
  echo "\t<?php echo \$this->element('webadmin_breadcrumb'); ?>\n";
}
if ($action == 'edit') {
  echo "\t<?php echo \$this->element('webadmin_related_tabs_labels'); ?>\n";
}
?>
  <div class="form">
    <div class="wrapper">
      <div class="subwrapper">
        <?php echo "<?php echo \$webAdmin->formCreate('{$modelClass}');?>\n";?>
  	      <fieldset>
<?php
echo "\t\t\t\t\t<?php\n";
$ignoreFields = array('created', 'modified', 'updated', 'lft', 'rght', 'child_count', 'direct_child_count', 'css_class',);
foreach ($schema as $field => $fieldInfo) {
  if (($action == 'add' && $field == $primaryKey)
  || in_array($field, $ignoreFields)
  || ($isOrderable && $field == $orderField)
  || ($action == 'add' && strstr($field, 'meta_'))
  || ($action == 'add' && $isAutoFillSummary && $field == $summaryField)) {
    continue;
  } else {
    echo "\t\t\t\t\t\techo \$webAdmin->formInput('{$field}');\n";
  }
}
if(!empty($associations['hasAndBelongsToMany'])) {
  foreach ($associations['hasAndBelongsToMany'] as $alias => $assocData) {
    echo "\t\t\t\t\t\techo \$webAdmin->formInput('{$alias}');\n";
  }
}
//echo "\t\techo \$jqueryForm->validate('".$modelClass.Inflector::humanize($action)."Form');\n";
echo "\t\t\t\t\t?>\n";
?>
  	      </fieldset>
<?php
if ($action == 'edit') {
  echo "\t\t\t\t<?php\n";
  echo "\t\t\t\techo \$webAdmin->formSubmit(__('Save', true), array('name' => 'submit'));\n";
  echo "\t\t\t\techo \$webAdmin->formEnd(array('name' => 'submit', 'label' => __('Save and Go Back', true)));\n";
  echo "\t\t\t\t?>\n";
} else {
  echo "\t\t\t\t<?php\n";
  echo "\t\t\t\tif (!\$ajax->isAjax() && !isset(\$this->params['requested'])) {\n";
  echo "\t\t\t\t\techo \$webAdmin->formSubmit(__('Save', true), array('name' => 'submit'));\n";
  echo "\t\t\t\t\techo \$webAdmin->formSubmit(__('Save and Add Another', true), array('name' => 'submit'));\n";
  echo "\t\t\t\t}\n";
  echo "\t\t\t\techo \$webAdmin->formEnd(array('name' => 'submit', 'label' => __('Save and Go Back', true)));\n";
  echo "\t\t\t\t?>\n";
}
?>
      </div>
    </div>
  </div>
<?php
if ($action == 'edit') {
  echo "\t<?php echo \$this->element('webadmin_related_tabs_contents'); ?>\n";
}
?>
</div>
