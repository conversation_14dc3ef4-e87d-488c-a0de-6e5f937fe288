<div id="<?php echo $pluralVar;?>Index" class="index">
  <div class="title clearfix">
    <h2><?php echo "<?php __('{$pluralHumanName}');?>";?></h2>
    <?php echo "<?php echo \$this->element('webadmin_actions'); ?>\n";?>
  </div>
<?php
echo "\t<?php\n";
echo "\t\$webAdmin->sessionFlash();\n";
echo "\tif(!empty(\${$pluralVar})) :\n";
if ($isTree) {
  echo "\t\techo \$this->element('webadmin_breadcrumb');\n";
} elseif (!$isOrderable) {
  echo "\t\techo \$this->element('webadmin_filter');\n";
  echo "\t\techo \$this->element('webadmin_pagination');\n";
}
echo "\t\t?>\n";
?>
    <div class="table">
      <table cellpadding="0" cellspacing="0"<?php if ($isTree || $orderField) echo ' id="'.$pluralVar.'Table"'; ?>>
        <thead>
          <tr>
<?php
if ($isTree || $isOrderable) {
  echo "\t\t\t\t\t\t<th class=\"reorder\"></th>\n";
}
$ignoreFields = array('lft', 'rght', 'child_count', 'direct_child_count', 'css_class', 'parent_id', 'image_id', 'meta_title',);
foreach ($schema as $field => $fieldInfo) {
  if (in_array($field, $ignoreFields)
  || ($isOrderable && $field == $orderField)
  || $fieldInfo['type'] == 'text'
  || ($isWith && $field == $primaryKey)
  || (isset($fieldInfo['hide_on_index']) && $fieldInfo['hide_on_index'])) {
    continue;
  }
  if ($isTree || $isOrderable) {
    echo "\t\t\t\t\t\t<th";
    if ($isWith && $field == 'featured') {
      echo ' class="featured"';
    }
    echo ">";
    __(Inflector::humanize(preg_replace('/_id$/', '', $field)));
    echo "</th>\n";
  } else {
    $isKey = false;
    if (!empty($associations['belongsTo'])) {
    	foreach ($associations['belongsTo'] as $alias => $fieldInfo) {
        if ($field === $fieldInfo['foreignKey']) {
          $isKey = true;
          $label = __(Inflector::humanize(preg_replace('/_id$/', '', $field)), true);
          echo "\t\t\t\t\t\t<th><?php echo \$paginator->sort('{$label}', '{$alias}.{$fieldInfo['displayField']}');?></th>\n";
          break;
        }
      }
    }
    if($isKey !== true) {
      echo "\t\t\t\t\t\t<th><?php echo \$paginator->sort('{$field}');?></th>\n";
    }
  }
}
echo "\t\t\t\t\t\t<th class=\"actions\"><?php __('Actions'); ?></th>\n";
?>
          </tr>
        </thead>
        <tbody>
<?php
echo "\t\t\t\t\t<?php\n";

echo "\t\t\t\t\t\$i = 0;\n";
echo "\t\t\t\t\tforeach (\${$pluralVar} as \${$singularVar}) :\n";
echo "\t\t\t\t\t\t\$class = null;\n";
echo "\t\t\t\t\t\tif (\$i++ % 2 == 0) {\n";
echo "\t\t\t\t\t\t\t\$class = ' class=\"altrow\"';\n";
echo "\t\t\t\t\t\t}\n";
echo "\t\t\t\t\t?>\n";

echo "\t\t\t\t\t<tr<?php echo \$class; ?>";
if ($isTree || $isOrderable) {
  echo " id=\"row-<?php echo \${$singularVar}['{$modelClass}']['{$primaryKey}']; ?>\"";
}
echo ">\n";
if ($isTree || $isOrderable) {
  echo "\t\t\t\t\t\t<td><span class=\"drag\">Drag</span></td>\n";
}
foreach ($schema as $field => $fieldInfo) {
  if (in_array($field, $ignoreFields)
  || ($isOrderable && $field == $orderField)
  || $fieldInfo['type'] == 'text'
  || ($isWith && $field == $primaryKey)
  || (isset($fieldInfo['hide_on_index']) && $fieldInfo['hide_on_index'])) {
    continue;
  }
  $isKey = false;
  if(!empty($associations['belongsTo'])) {
  	foreach ($associations['belongsTo'] as $alias => $assocData) {
      if($field === $assocData['foreignKey']) {
        $isKey = true;
        echo "\t\t\t\t\t\t<td><?php echo \$webAdmin->htmlLinkIfPermitted(\${$singularVar}['{$alias}']['{$assocData['displayField']}'], array('controller'=> '{$assocData['controllerPath']}', 'action'=>'edit', \${$singularVar}['{$alias}']['{$assocData['primaryKey']}'])); ?></td>\n";
        break;
      }
  	}
  }
  if($isKey !== true) {
    if ($isTree && $field == $displayField) {
      echo "\t\t\t\t\t\t<td>\n";
      echo "\t\t\t\t\t\t\t<?php\n";
      echo "\t\t\t\t\t\t\tif (\${$singularVar}['{$modelClass}']['child_count']) {\n";
      echo "\t\t\t\t\t\t\t\t\$linkClass = 'has-children';\n";
      echo "\t\t\t\t\t\t\t} else {\n";
      echo "\t\t\t\t\t\t\t\t\$linkClass = 'no-children';\n";
      echo "\t\t\t\t\t\t\t}\n";
      echo "\t\t\t\t\t\t\techo \$webAdmin->htmlLinkIfPermitted(\${$singularVar}['{$modelClass}']['{$field}'], array(\${$singularVar}['{$modelClass}']['{$primaryKey}']), array('class' => \$linkClass));\n";
      echo "\t\t\t\t\t\t\t?>\n";
      echo "\t\t\t\t\t\t</td>\n";
    } elseif ($isWith && $field == 'featured') {
      echo "\t\t\t\t\t\t<td>\n";
      echo "\t\t\t\t\t\t\t<?php\n";
      echo "\t\t\t\t\t\t\tif (\${$singularVar}['{$modelClass}']['featured']) {\n";
      echo "\t\t\t\t\t\t\t\t\$linkClass = 'feature-link featured';\n";
      echo "\t\t\t\t\t\t\t} else {\n";
      echo "\t\t\t\t\t\t\t\t\$linkClass = 'feature-link';\n";
      echo "\t\t\t\t\t\t\t}\n";
      echo "\t\t\t\t\t\t\techo \$webAdmin->htmlLinkIfPermitted(\${$singularVar}['{$modelClass}']['{$field}'], array('action' => 'toggle_field', '{$field}', \${$singularVar}['{$modelClass}']['{$primaryKey}']), array('class' => \$linkClass));\n";
      echo "\t\t\t\t\t\t\t?>\n";
      echo "\t\t\t\t\t\t</td>\n";
    } elseif ($schema[$field]['type'] == 'datetime') {
      echo "\t\t\t\t\t\t<td><?php echo \$time->niceShort(\${$singularVar}['{$modelClass}']['{$field}']); ?></td>\n";
    } else {
      echo "\t\t\t\t\t\t<td><?php echo \${$singularVar}['{$modelClass}']['{$field}']; ?></td>\n";
    }
  }
}
echo "\t\t\t\t\t\t<td class=\"actions\">\n";
if (!$isWith) {
  //echo "\t\t\t\t\t\t\t<?php echo \$webAdmin->htmlLinkIfPermitted(__('View', true), array('action'=>'view', \${$singularVar}['{$modelClass}']['{$primaryKey}']), array('class' => 'view')); ? >\n";
  echo "\t\t\t\t\t\t\t<?php echo \$webAdmin->htmlLinkIfPermitted(__('Edit', true), array('action'=>'edit', \${$singularVar}['{$modelClass}']['{$primaryKey}']), array('class' => 'edit')); ?>\n";
}
echo "\t\t\t\t\t\t\t<?php echo \$webAdmin->htmlLinkIfPermitted(__('Delete', true), array('action'=>'delete', \${$singularVar}['{$modelClass}']['{$primaryKey}']), array('class' => 'delete'), sprintf(__('Are you sure you want to delete # %s?', true), \${$singularVar}['{$modelClass}']['{$primaryKey}'])); ?>\n";
echo "\t\t\t\t\t\t</td>\n";
echo "\t\t\t\t\t</tr>\n";
echo "\t\t\t\t\t<?php endforeach; ?>\n";
?>
        </tbody>
      </table>
    </div>
<?php
if (!$isTree && !$isOrderable) {
  echo "\t\t<?php echo \$this->element('webadmin_pagination'); ?>\n";
}
if ($isTree || $isOrderable) {
  $sortableField = null;
  if ($orderField && $orderField <> 'order') {
    $sortableField = ", field: '{$orderField}'";
  }
  echo "\t\t<?php echo \$javascript->codeBlock(\"new Webadmin.SortableTable('{$pluralVar}Table', {controller: '".Inflector::tableize($modelClass)."', model: '{$modelClass}'{$sortableField}});\", array('inline' => false)); ?>\n";
}
echo "\t<?php else : ?>\n";
echo "\t\t<p class=\"not-found\">".__('No results found', true)."</p>\n";
echo "\t<?php endif; ?>\n";
?>
</div>
