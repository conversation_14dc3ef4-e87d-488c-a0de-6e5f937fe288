<?php

class ImportShell extends Shell {

  var $settings = array(
    'import_types' => array(
      'i' => array(
        'task' => 'ImportImages',
        'folder_model' => 'ImageFolder',
        'model' => 'Image',
      )
    )
  );

  var $type;

  var $base;

  var $underFolder = null;

  var $folders;

  var $files;

  function main() {

    parent::loadTasks();

    $this->out('Import Shell');
    $this->hr();
    $this->out('[i]mages');
    $this->out('[f]iles');
    $this->out('[q]uit');

    $type = strtolower($this->in(__('What would you like to import?', true), array('i', 'f', 'q')));

    if ($type == 'q') {
      $this->_stop();
    } elseif (array_key_exists($type, $this->settings['import_types'])) {
      $this->type = $this->settings['import_types'][$type];
      $this->import();
    } else {
      $this->out(__('You have made an invalid selection. Please choose a valid import type by entering \'i\' or \'f\'.', true));
    }

    $this->hr();
    $this->main();

  }

  function import() {

    $this->getFoldersAndFiles();

    $this->getUnderFolder();

    $this->importFolders();

    $this->importFiles();

  }

  function getFoldersAndFiles() {

    $path = '';

    while ($path == '') {

      $path = $this->in(__('Enter path to root folder on file system to import or [q] to quit', true), null, 'q');

      if (strtolower($path) === 'q') {
        $this->_stop();
      }

      if (!empty($path) && (!is_dir($path) || !is_readable($path))) {
        $this->out(__('The path you supplied does not exist or is not readable. Please try again.', true));
        $path = '';
      }

    }

    $this->base = trim($path, ' /\\');

    App::import('Core', 'Folder');

    $this->Folder = new Folder($this->base);

    list($folders, $files) = $this->Folder->tree($this->base);

    $proceed = strtolower($this->in(__('There are ' . count($folders) . ' sub folders and ' . count($files) . ' files - enter [q] to quit or any other key to proceed', true)));

    if ($proceed == 'q') {
      $this->_stop();
    }

    $this->folders = array_flip($folders);

    $this->files = $files;

  }

  function getUnderFolder() {

    $folderModel = $this->type['folder_model'];

    App::import('Model', $folderModel);

    $this->FolderModel = new $folderModel();

    $folders = $this->FolderModel->generatetreelist();

    foreach ($folders as $folderId => $folderName) {
      $this->out($folderId . '. ' . $folderName);
    }

    $underFolder = '';

    while ($underFolder == '') {

      $underFolder = $this->in(__('Enter a number from the list above, or \'q\' to quit', true), null, 'q');

      if (strtolower($underFolder) === 'q') {
        $this->_stop();
      }

      if ($underFolder == '' || !array_key_exists(intval($underFolder), $folders)) {
        $this->out(__('The number you supplied was empty, or was not an option. Please try again.', true));
        $underFolder = '';
      }

    }

    $this->underFolder = $underFolder;

  }

  function importFolders() {

    foreach ($this->folders as $path => $dummy) {

      if ($path == $this->base) {
        $this->folders[$path] = $this->underFolder;
      } else {
        $this->folders[$path] = $this->_getFolderId($path);
      }

    }

  }

  function _getFolderId($path) {

    $folderName = basename($path);

    $parentPath = dirname($path);

    $parentId = $this->folders[$parentPath];

    $existingFolder = $this->FolderModel->find('first', array(
      'conditions' => array(
        'parent_id' => $parentId,
        $this->FolderModel->displayField => $folderName,
      ),
      'recursive' => -1,
    ));

    if ($existingFolder) {
      return $existingFolder[$this->FolderModel->alias][$this->FolderModel->primaryKey];
    }

    $newFolderData = array(
      $this->FolderModel->alias => array(
        'parent_id' => $parentId,
        $this->FolderModel->displayField => $folderName,
      )
    );

    $this->FolderModel->create();
    if ($this->FolderModel->save($newFolderData)) {
      return $this->FolderModel->getInsertID();
    }

    return false;

  }

  function importFiles() {

    $model = $this->type['model'];

    App::import('Model', $model);

    $this->Model = new $model();

    $fileField = $this->Model->Behaviors->Upload->settings[$this->Model->alias]['file_field'];

    $folderIdField = $this->Model->belongsTo[$this->type['folder_model']]['foreignKey'];

    foreach ($this->files as $file) {

      $uniqueFileName = CakeString::uuid();

      copy($file, TMP.$uniqueFileName);

      $folderId = $this->folders[dirname($file)];

      $this->Model->create();

      $data = array(
        $this->Model->alias => array(
          $folderIdField => $folderId,
          $fileField => array(
            'error' => 0,
            'name' => basename($file),
            'tmp_name' => $uniqueFileName,
          )
        )
      );

      $this->Model->save($data);

      $this->out($this->Model->getInsertID());

    }

  }

}

?>