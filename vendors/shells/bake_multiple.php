<?php

App::import('Core', 'bake');

class BakeMultipleShell extends BakeShell {

  var $tasks = array('ControllerMultiple', 'ViewMultiple', 'ModelFullValidate');

  function main() {

    parent::loadTasks();

		$this->out('Bake Multiple Shell');
		$this->hr();
		$this->out('[M]odel');
		$this->out('[V]iew');
		$this->out('[C]ontroller');
		$this->out('[Q]uit');

		$classToBake = strtoupper($this->in(__('What would you like to Bake?', true), array('M', 'V', 'C', 'Q')));

		switch($classToBake) {
			case 'M':
				$this->ModelFullValidate->execute();
				break;
			case 'V':
				$this->ViewMultiple->execute();
				break;
			case 'C':
				$this->ControllerMultiple->execute();
				break;
			case 'Q':
				exit(0);
				break;
			default:
				$this->out(__('You have made an invalid selection. Please choose a type of class to Bake by entering M, V or C.', true));
		}
		$this->hr();
		$this->main();
  }

}

?>