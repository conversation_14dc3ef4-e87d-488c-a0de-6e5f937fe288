<?php

App::import('Vendor', 'shells/tasks/view');

class ViewMultipleTask extends ViewTask {

  var $tasks = array('ControllerMultiple');

  var $actions = array();

  function execute() {

    parent::loadTasks();

    $this->getActions();

    foreach ($this->ControllerMultiple->getNames() as $controllerName) {
      $this->controllerName = $controllerName;
      $this->controllerPath = low(Inflector::underscore($controllerName));
      $this->bake();
    }

    $this->out(__("View Baking Complete.\n", true));

  }

  function getActions() {

    $this->out(__("What actions should we create views for?", true));

    $admin = $this->getAdmin();

    $allAdmin = array();
    $allNonAdmin = array();
    foreach ($this->scaffoldActions as $action) {
      $allAdmin[] = $admin . $action;
      $allNonAdmin[] = $action;
    }
    $all = array_merge($allAdmin, $allNonAdmin);

    $possibleActions = array(
      array(
        'label' => 'All',
        'actions' => $all,
      ),
      array(
        'label' => "All $admin",
        'actions' => $allAdmin,
      ),
      array(
        'label' => "{$admin}add and {$admin}edit",
        'actions' => array(
          $admin.'add',
          $admin.'edit',
        ),
      ),
      array(
        'label' => "All non $admin",
        'actions' => $allNonAdmin,
      ),
      array(
        'label' => "add and edit",
        'actions' => array(
          'add',
          'edit',
        ),
      ),
    );

    $possibleCount = count($possibleActions);

    $actionsCount = count($this->scaffoldActions);

    for ($i=0; $i<$actionsCount; $i++) {
      $action = $this->scaffoldActions[$i];
      $possibleActions[$possibleCount+$i] = array(
        'label' => $admin . $action,
        'actions' => array($admin . $action),
      );
      $possibleActions[$possibleCount+$actionsCount+$i] = array(
        'label' => $action,
        'actions' => array($action),
      );
    }

    $possibleCount = count($possibleActions);

    for ($i=0; $i<$possibleCount; $i++) {
      $this->out($i + 1 . '. ' . __($possibleActions[$i]['label'], true));
    }

    $enteredAction = '';

    while ($enteredAction == '') {

      $enteredAction = $this->in(__("Enter a number from the list above, or 'q' to exit", true), null, 'q');

      if ($enteredAction === 'q') {
        $this->out(__("Exit", true));
        $this->_stop();
      }

      if ($enteredAction == '' || intval($enteredAction) > $possibleCount) {
        $this->out(__('Error:', true));
        $this->out(__("The number you supplied was empty, or  was not an option. Please try again.", true));
        $enteredAction = '';
      }
    }

    $this->actions = $possibleActions[intval($enteredAction) - 1]['actions'];

  }

  function bake() {

    foreach ($this->actions as $action) {
      $this->template = $action;
      parent::bake($action, true);
    }

    $this->hr();
  }

  function __loadController() {

    if (!$this->controllerName) {
      $this->err(__('Controller not found', true));
    }

    $import = $this->controllerName;

    if ($this->plugin) {
      $import = $this->plugin . '.' . $this->controllerName;
    }

    if (!App::import('Controller', $import)) {
      $file = $this->controllerPath . '_controller.php';
      $this->err(sprintf(__("The file '%s' could not be found.\nIn order to bake a view, you'll need to first create the controller.", true), $file));
      $this->_stop();
    }

    $controllerClassName = $this->controllerName . 'Controller';
    $controllerObj = & new $controllerClassName();
    $controllerObj->constructClasses();
    $modelClass = $controllerObj->modelClass;
    $modelObj =& ClassRegistry::getObject($controllerObj->modelKey);

    if (is_a($modelObj, 'AppModel')) {
      App::import('Model', $modelClass);
      $modelObj = new $modelClass;
    }

    if ($modelObj) {

      return $modelObj->getInfo();

    } else {
      $primaryKey = $displayField = null;
      $fields = $schema = $associations = array();
      $isTree = $isOrderable = $isAutoFillSummary = $isWith = false;
      $singularVar = Inflector::variable(Inflector::singularize($this->controllerName));
      $pluralVar = Inflector::variable($this->controllerName);
      $singularHumanName = Inflector::humanize(Inflector::underscore($singularVar));
      $pluralHumanName = Inflector::humanize(Inflector::underscore($pluralVar));
      return compact('modelClass', 'schema', 'primaryKey', 'displayField', 'singularVar', 'pluralVar',
          'singularHumanName', 'pluralHumanName', 'fields', 'associations', 'isTree', 'isOrderable', 'isAutoFillSummary', 'isWith');
    }

  }

}

?>