<?php

App::import('Model', 'connection_manager');
App::import('Vendor', 'shells/tasks/model');

class ModelFullValidateTask extends ModelTask {

  var $validators = array();

  var $guesses = array();

  var $skip = 'q';

  var $requiredValues = array(
    'y' => 'true',
    'n' => 'false',
  );

  var $allowEmptyValues = array(
    'y' => 'true',
    'n' => 'false',
  );

  var $onValues = array(
    'c' => 'create',
    'u' => 'update',
    'b' => 'null',
  );

  var $requiredDefault = 'n';

  var $allowEmptyDefault = 'n';

  var $onDefault = 'b';

  /**
   * Handles validation
   *
   * @param object $model
   * @param boolean $interactive
   * @return array $validate
   * @access public
   */
  function doValidation(&$model, $interactive = true) {

    if (!is_object($model)) {
      return false;
    }

    $fields = $model->schema();

    if (empty($fields)) {
      return false;
    }

    $this->loadValidators();

    if (empty($this->validators)) {
      return false;
    }

    $validate = array();

    foreach ($fields as $fieldName => $field) {

      $db =& ConnectionManager::getDataSource('default');
      mysqli_ping($db->connection);

      $choice = null;

      while ($choice != $this->skip) {

        $prompt = $this->prompt($fieldName, $field);

        $guess = $this->skip;

        if ($fieldName <> $model->primaryKey && !in_array($fieldName, array('created', 'modified', 'updated'))) {

          $attemptGuess = (string)$this->guessRule($fieldName, $field);

          if (array_key_exists($attemptGuess, $this->guesses)) {
            $guess = $this->guesses[$attemptGuess];
          }

        }

        if ($interactive === true) {
          $this->out('');
          $choice = $this->in($prompt, null, $guess);
        } else {
          $choice = $guess;
        }

        if ($choice != $this->skip) {

          if (is_numeric($choice) && isset($this->validators[$choice])) {

            $rule = $params = array();

            foreach ($this->validators[$choice]['params'] as $param => $default) {
              $params[] = $this->in($param, null, $default);
            }

            $params = array_filter($params);

            $messageExtra = null;

            if(empty($params)) {

              $rule['rule'] = $this->validators[$choice]['rule'];

            } else {

              $rule['rule'] = array($this->validators[$choice]['rule']);

              foreach($params as $value) {
                $rule['rule'][] = $value;
              }

              $messageExtra .= ' ' . implode(__(' and ', true), $params);

            }

            $rule['required'] = $this->requiredValues[$this->in('Required? [n]o or [y]es:', null, $this->requiredDefault)];

            $rule['allowEmpty'] = $this->allowEmptyValues[$this->in('Allow Empty? [n]o or [y]es:', null, $this->allowEmptyDefault)];

            $rule['on'] = $this->onValues[$this->in('On? [c]reate or [u]pdate or [b]oth:', null, $this->onDefault)];

            $rule['message'] = __('The '.Inflector::humanize($fieldName).' must be '.Inflector::humanize(Inflector::underscore($this->validators[$choice]['rule'])), true) . $messageExtra;

            $validate[$fieldName][$this->validators[$choice]['rule']] = $rule;

          } else {

            $validate[$fieldName] = $choice;

          }

        }

      }

    }

    return $validate;

  }

  function loadValidators() {

    if (!class_exists('Validation')) {
      return false;
    }

    $rf = new ReflectionClass('Validation');

    $methods = $rf->getMethods();

    if (empty($methods)) {
      return false;
    }

    $i=1;

    foreach ($methods as $method) {

      $methodName = $method->getName();

      if (substr($methodName,0,1) == '_'
      || $method->isPublic() == false
      || $method->getDeclaringClass()->getName() != 'Validation'
      || $methodName == 'getInstance') {
        continue;
      }

      $params = array();

      foreach ($method->getParameters() as $param) {

        $paramName = $param->getName();

        if (substr($paramName,0,5) == 'check') {
          continue;
        }

        if ($param->isDefaultValueAvailable()) {
          $params[$paramName] = $param->getDefaultValue();
        } else {
          $params[$paramName] = null;
        }

      }

      $this->validators[$i] = array(
      	'rule' => $methodName,
      	'params'=>$params
      );
      $this->guesses[(string)$methodName] = $i;

      $i++;

    }

  }

  function guessRule($fieldName, $field) {

    if ($fieldName == 'email') {
      return 'email';
    } elseif ($field['type'] == 'string') {
      return 'notEmpty';
    } elseif ($field['type'] == 'integer') {
      return 'numeric';
    } elseif ($field['type'] == 'boolean') {
      return 'boolean';
    } elseif ($field['type'] == 'datetime') {
      return 'date';
    }

    return false;

  }

  function prompt($fieldName, $field) {

    $prompt = 'Name: ' . $fieldName . "\n";
    $prompt .= 'Type: ' . $field['type'] . "\n";
    $prompt .= '---------------------------------------------------------------'."\n";
    $prompt .= 'Please select one of the following validation options:'."\n";
    $prompt .= '---------------------------------------------------------------'."\n";

    foreach ($this->validators as $key => $validator) {
      $prompt .= str_pad("$key. {$validator['rule']}", 18, ' ', STR_PAD_RIGHT);
      $prompt .= $key % 3 == 0 ? "\n" : "\t";
    }

    $prompt .= "q - Do not do any more validation on this field.\n\n";
    $prompt .= "... or enter in a valid regex validation string.\n\n";

    return $prompt;

  }

  function bakeValidation($validate) {

    $out = '';

    if (count($validate)) {
      $out .= "\tvar \$validate = array(\n";
      foreach ($validate as $field => $rules) {
        $out .= "\t\t'{$field}' => array(\n";
        foreach ($rules as $key => $rule) {
          $out .= "\t\t\t'$key' => array(\n";
          foreach ($rule as $key => $params) {
            $out .= "\t\t\t\t'$key' => ";
            if ($key=='rule'
            && is_array($params)) {
              $out .= 'array(';
              foreach ($params as $param) {
                if (is_numeric($param)
                || in_array($param, array('true', 'false', 'null'))) {
                  $out .= $param . ', ';
                } else {
                  $out .= "'$param', ";
                }
              }
              $out .= "),\n";
            } elseif ($key == 'message') {
              $out .= sprintf("'$params',\n", Inflector::humanize($field));
            } else {
              if (is_numeric($params)
              || in_array($params, array('true', 'false', 'null'))) {
                $out .= "$params,\n";
              } else {
                $out .= "'$params',\n";
              }
            }
          }
          $out .= "\t\t\t),\n";
        }
        $out .= "\t\t),\n";
      }
      $out .= "\t);\n\n";
    }

    return $out;

  }

}

?>