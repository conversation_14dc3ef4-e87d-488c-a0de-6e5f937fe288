<?php

App::import('Model', 'connection_manager');
App::import('Vendor', 'shells/tasks/controller');

class ControllerMultipleTask extends ControllerTask {

  function execute() {

    foreach ($this->getNames() as $controllerName) {
      $this->bake($controllerName);
    }

    $this->out('Controller Baking Complete.'."\n");

  }

  function getAll($useDbConfig = 'default') {
    $db =& ConnectionManager::getDataSource($useDbConfig);
    $usePrefix = empty($db->config['prefix']) ? '' : $db->config['prefix'];
    if ($usePrefix) {
      $tables = array();
      foreach ($db->listSources() as $table) {
        if (!strncmp($table, $usePrefix, strlen($usePrefix))) {
          $tables[] = substr($table, strlen($usePrefix));
        }
      }
    } else {
      $tables = $db->listSources();
    }

    if (empty($tables)) {
      $this->err(__('Your database does not have any tables.', true));
      $this->_stop();
    }

    $this->_controllerNames = array();
    $count = count($tables);
    for ($i = 0; $i < $count; $i++) {
      $model = $this->_modelName($tables[$i]);
      if (App::import('Model', $model)) {
        $this->_controllerNames[] = $this->_controllerName($model);
      }
    }
    return $this->_controllerNames;
  }

  function getNames() {

    $useDbConfig = 'default';

    $controllers = $this->listAll($useDbConfig, 'Controllers');

    $enteredController = '';

    while ($enteredController == '') {

      $enteredController = $this->in(__("Enter one or more numbers from the list above separated by a space, or type in the names of one or more other controllers, 'a' for all or 'q' to exit", true), null, 'q');

      if (strtolower($enteredController) === 'q') {
        $this->out(__("Exit", true));
        $this->_stop();
      } elseif ($enteredController == '') {
        $this->out(__('Error:', true));
        $this->out(__("The Controller name you supplied was empty. Please try again.", true));
      }

    }

    if (strtolower($enteredController) == 'a') {
      return $this->getAll();
    }

    if (preg_match('/[^a-z0-9]/i', $enteredController)) {
      $enteredControllers = preg_split('/[^a-z0-9]/i', $enteredController);
    } else {
      $enteredControllers = array($enteredController);
    }

    $controllerNames = array();

    foreach ($enteredControllers as $enteredController) {
      if (intval($enteredController) > 0 && intval($enteredController) <= count($controllers) ) {
        $controllerNames[] = $controllers[intval($enteredController) - 1];
      } else {
        $controllerNames[] = Inflector::camelize($enteredController);
      }
    }

    return $controllerNames;

  }


}

?>