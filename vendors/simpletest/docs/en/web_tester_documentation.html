<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Simple Test for PHP web script testing documentation</title>
<link rel="stylesheet" type="text/css" href="docs.css" title="Styles">
</head>
<body>
<div class="menu_back"><div class="menu">
<a href="index.html">SimpleTest</a>
                |
                <a href="overview.html">Overview</a>
                |
                <a href="unit_test_documentation.html">Unit tester</a>
                |
                <a href="group_test_documentation.html">Group tests</a>
                |
                <a href="mock_objects_documentation.html">Mock objects</a>
                |
                <a href="partial_mocks_documentation.html">Partial mocks</a>
                |
                <a href="reporter_documentation.html">Reporting</a>
                |
                <a href="expectation_documentation.html">Expectations</a>
                |
                <span class="chosen">Web tester</span>
                |
                <a href="form_testing_documentation.html">Testing forms</a>
                |
                <a href="authentication_documentation.html">Authentication</a>
                |
                <a href="browser_documentation.html">Scriptable browser</a>
</div></div>
<h1>Web tester documentation</h1>
        This page...
        <ul>
<li>
            Successfully <a href="#fetch">fetching a web page</a>
        </li>
<li>
            Testing the <a href="#content">page content</a>
        </li>
<li>
            <a href="#navigation">Navigating a web site</a>
            while testing
        </li>
<li>
            <a href="#request">Raw request modifications</a> and debugging methods
        </li>
</ul>
<div class="content">
        <p><a class="target" name="fetch"><h2>Fetching a page</h2></a></p>
            <p>
                Testing classes is all very well, but PHP is predominately
                a language for creating functionality within web pages.
                How do we test the front end presentation role of our PHP
                applications?
                Well the web pages are just text, so we should be able to
                examine them just like any other test data.
            </p>
            <p>
                This leads to a tricky issue.
                If we test at too low a level, testing for matching tags
                in the page with pattern matching for example, our tests will
                be brittle.
                The slightest change in layout could break a large number of
                tests.
                If we test at too high a level, say using mock versions of a
                template engine, then we lose the ability to automate some classes
                of test.
                For example, the interaction of forms and navigation will
                have to be tested manually.
                These types of test are extremely repetitive and error prone.
            </p>
            <p>
                SimpleTest includes a special form of test case for the testing
                of web page actions.
                The <span class="new_code">WebTestCase</span> includes facilities
                for navigation, content and cookie checks and form handling.
                Usage of these test cases is similar to the
                <a href="unit_tester_documentation.html">UnitTestCase</a>...
<pre>
<strong>class TestOfLastcraft extends WebTestCase {
}</strong>
</pre>
                Here we are about to test the
                <a href="http://www.lastcraft.com/">Last Craft</a> site itself.
                If this test case is in a file called <em>lastcraft_test.php</em>
                then it can be loaded in a runner script just like unit tests...
<pre>
&lt;?php
require_once('simpletest/autorun.php');<strong>
require_once('simpletest/web_tester.php');</strong>
SimpleTest::prefer(new TextReporter());

class WebTests extends TestSuite {
    function WebTests() {
        $this-&gt;TestSuite('Web site tests');<strong>
        $this-&gt;addFile('lastcraft_test.php');</strong>
    }
}
?&gt;
</pre>
                I am using the text reporter here to more clearly
                distinguish the web content from the test output.
            </p>
            <p>
                Nothing is being tested yet.
                We can fetch the home page by using the
                <span class="new_code">get()</span> method...
<pre>
class TestOfLastcraft extends WebTestCase {
    <strong>
    function testHomepage() {
        $this-&gt;assertTrue($this-&gt;get('http://www.lastcraft.com/'));
    }</strong>
}
</pre>
                The <span class="new_code">get()</span> method will
                return true only if page content was successfully
                loaded.
                It is a simple, but crude way to check that a web page
                was actually delivered by the web server.
                However that content may be a 404 response and yet
                our <span class="new_code">get()</span> method will still return true.
            </p>
            <p>
                Assuming that the web server for the Last Craft site is up
                (sadly not always the case), we should see...
<pre class="shell">
Web site tests
OK
Test cases run: 1/1, Failures: 0, Exceptions: 0
</pre>
                All we have really checked is that any kind of page was
                returned.
                We don't yet know if it was the right one.
            </p>
        
        <p><a class="target" name="content"><h2>Testing page content</h2></a></p>
            <p>
                To confirm that the page we think we are on is actually the
                page we are on, we need to verify the page content.
<pre>
class TestOfLastcraft extends WebTestCase {
    
    function testHomepage() {<strong>
        $this-&gt;get('http://www.lastcraft.com/');
        $this-&gt;assertText('Why the last craft');</strong>
    }
}
</pre>
                The page from the last fetch is held in a buffer in
                the test case, so there is no need to refer to it directly.
                The pattern match is always made against the buffer.
            </p>
            <p>
                Here is the list of possible content assertions...
                <table><tbody>
                    <tr>
<td><span class="new_code">assertTitle($title)</span></td>
<td>Pass if title is an exact match</td>
</tr>
                    <tr>
<td><span class="new_code">assertText($text)</span></td>
<td>Pass if matches visible and "alt" text</td>
</tr>
                    <tr>
<td><span class="new_code">assertNoText($text)</span></td>
<td>Pass if doesn't match visible and "alt" text</td>
</tr>
                    <tr>
<td><span class="new_code">assertPattern($pattern)</span></td>
<td>A Perl pattern match against the page content</td>
</tr>
                    <tr>
<td><span class="new_code">assertNoPattern($pattern)</span></td>
<td>A Perl pattern match to not find content</td>
</tr>
                    <tr>
<td><span class="new_code">assertLink($label)</span></td>
<td>Pass if a link with this text is present</td>
</tr>
                    <tr>
<td><span class="new_code">assertNoLink($label)</span></td>
<td>Pass if no link with this text is present</td>
</tr>
                    <tr>
<td><span class="new_code">assertLinkById($id)</span></td>
<td>Pass if a link with this id attribute is present</td>
</tr>
                    <tr>
<td><span class="new_code">assertNoLinkById($id)</span></td>
<td>Pass if no link with this id attribute is present</td>
</tr>
                    <tr>
<td><span class="new_code">assertField($name, $value)</span></td>
<td>Pass if an input tag with this name has this value</td>
</tr>
                    <tr>
<td><span class="new_code">assertFieldById($id, $value)</span></td>
<td>Pass if an input tag with this id has this value</td>
</tr>
                    <tr>
<td><span class="new_code">assertResponse($codes)</span></td>
<td>Pass if HTTP response matches this list</td>
</tr>
                    <tr>
<td><span class="new_code">assertMime($types)</span></td>
<td>Pass if MIME type is in this list</td>
</tr>
                    <tr>
<td><span class="new_code">assertAuthentication($protocol)</span></td>
<td>Pass if the current challenge is this protocol</td>
</tr>
                    <tr>
<td><span class="new_code">assertNoAuthentication()</span></td>
<td>Pass if there is no current challenge</td>
</tr>
                    <tr>
<td><span class="new_code">assertRealm($name)</span></td>
<td>Pass if the current challenge realm matches</td>
</tr>
                    <tr>
<td><span class="new_code">assertHeader($header, $content)</span></td>
<td>Pass if a header was fetched matching this value</td>
</tr>
                    <tr>
<td><span class="new_code">assertNoHeader($header)</span></td>
<td>Pass if a header was not fetched</td>
</tr>
                    <tr>
<td><span class="new_code">assertCookie($name, $value)</span></td>
<td>Pass if there is currently a matching cookie</td>
</tr>
                    <tr>
<td><span class="new_code">assertNoCookie($name)</span></td>
<td>Pass if there is currently no cookie of this name</td>
</tr>
                </tbody></table>
                As usual with the SimpleTest assertions, they all return
                false on failure and true on pass.
                They also allow an optional test message and you can embed
                the original test message inside using "%s" inside
                your custom message.
            </p>
            <p>
                So now we could instead test against the title tag with...
<pre>
<strong>$this-&gt;assertTitle('The Last Craft? Web developer tutorials on PHP, Extreme programming and Object Oriented development');</strong>
</pre>
                ...or, if that is too long and fragile...
<pre>
<strong>$this-&gt;assertTitle(new PatternExpectation('/The Last Craft/'));</strong>
</pre>
                As well as the simple HTML content checks we can check
                that the MIME type is in a list of allowed types with...
<pre>
<strong>$this-&gt;assertMime(array('text/plain', 'text/html'));</strong>
</pre>
                More interesting is checking the HTTP response code.
                Like the MIME type, we can assert that the response code
                is in a list of allowed values...
<pre>
class TestOfLastcraft extends WebTestCase {
    
    function testRedirects() {
        $this-&gt;get('http://www.lastcraft.com/test/redirect.php');
        $this-&gt;assertResponse(200);&lt;/strong&gt;
    }
}
</pre>
                Here we are checking that the fetch is successful by
                allowing only a 200 HTTP response.
                This test will pass, but it is not actually correct to do so.
                There is no page, instead the server issues a redirect.
                The <span class="new_code">WebTestCase</span> will
                automatically follow up to three such redirects.
                The tests are more robust this way and we are usually
                interested in the interaction with the pages rather
                than their delivery.
                If the redirects are of interest then this ability must
                be disabled...
<pre>
class TestOfLastcraft extends WebTestCase {
    
    function testHomepage() {<strong>
        $this-&gt;setMaximumRedirects(0);</strong>
        $this-&gt;get('http://www.lastcraft.com/test/redirect.php');
        $this-&gt;assertResponse(200);
    }
}
</pre>
                The assertion now fails as expected...
<pre class="shell">
Web site tests
1) Expecting response in [200] got [302]
    in testhomepage
    in testoflastcraft
    in lastcraft_test.php
FAILURES!!!
Test cases run: 1/1, Failures: 1, Exceptions: 0
</pre>
                We can modify the test to correctly assert redirects with...
<pre>
class TestOfLastcraft extends WebTestCase {
    
    function testHomepage() {
        $this-&gt;setMaximumRedirects(0);
        $this-&gt;get('http://www.lastcraft.com/test/redirect.php');
        $this-&gt;assertResponse(<strong>array(301, 302, 303, 307)</strong>);
    }
}
</pre>
                This now passes.
            </p>
        
        <p><a class="target" name="navigation"><h2>Navigating a web site</h2></a></p>
            <p>
                Users don't often navigate sites by typing in URLs, but by
                clicking links and buttons.
                Here we confirm that the contact details can be reached
                from the home page...
<pre>
class TestOfLastcraft extends WebTestCase {
    ...
    function testContact() {
        $this-&gt;get('http://www.lastcraft.com/');<strong>
        $this-&gt;clickLink('About');
        $this-&gt;assertTitle(new PatternExpectation('/About Last Craft/'));</strong>
    }
}
</pre>
                The parameter is the text of the link.
            </p>
            <p>
                If the target is a button rather than an anchor tag, then
                <span class="new_code">clickSubmit()</span> can be used
                with the button title...
<pre>
<strong>$this-&gt;clickSubmit('Go!');</strong>
</pre>
                If you are not sure or don't care, the usual case, then just
                use the <span class="new_code">click()</span> method...
<pre>
<strong>$this-&gt;click('Go!');</strong>
</pre>
            </p>
            <p>
                The list of navigation methods is...
                <table><tbody>
                    <tr>
<td><span class="new_code">getUrl()</span></td>
<td>The current location</td>
</tr>
                    <tr>
<td><span class="new_code">get($url, $parameters)</span></td>
<td>Send a GET request with these parameters</td>
</tr>
                    <tr>
<td><span class="new_code">post($url, $parameters)</span></td>
<td>Send a POST request with these parameters</td>
</tr>
                    <tr>
<td><span class="new_code">head($url, $parameters)</span></td>
<td>Send a HEAD request without replacing the page content</td>
</tr>
                    <tr>
<td><span class="new_code">retry()</span></td>
<td>Reload the last request</td>
</tr>
                    <tr>
<td><span class="new_code">back()</span></td>
<td>Like the browser back button</td>
</tr>
                    <tr>
<td><span class="new_code">forward()</span></td>
<td>Like the browser forward button</td>
</tr>
                    <tr>
<td><span class="new_code">authenticate($name, $password)</span></td>
<td>Retry after a challenge</td>
</tr>
                    <tr>
<td><span class="new_code">restart()</span></td>
<td>Restarts the browser as if a new session</td>
</tr>
                    <tr>
<td><span class="new_code">getCookie($name)</span></td>
<td>Gets the cookie value for the current context</td>
</tr>
                    <tr>
<td><span class="new_code">ageCookies($interval)</span></td>
<td>Ages current cookies prior to a restart</td>
</tr>
                    <tr>
<td><span class="new_code">clearFrameFocus()</span></td>
<td>Go back to treating all frames as one page</td>
</tr>
                    <tr>
<td><span class="new_code">clickSubmit($label)</span></td>
<td>Click the first button with this label</td>
</tr>
                    <tr>
<td><span class="new_code">clickSubmitByName($name)</span></td>
<td>Click the button with this name attribute</td>
</tr>
                    <tr>
<td><span class="new_code">clickSubmitById($id)</span></td>
<td>Click the button with this ID attribute</td>
</tr>
                    <tr>
<td><span class="new_code">clickImage($label, $x, $y)</span></td>
<td>Click an input tag of type image by title or alt text</td>
</tr>
                    <tr>
<td><span class="new_code">clickImageByName($name, $x, $y)</span></td>
<td>Click an input tag of type image by name</td>
</tr>
                    <tr>
<td><span class="new_code">clickImageById($id, $x, $y)</span></td>
<td>Click an input tag of type image by ID attribute</td>
</tr>
                    <tr>
<td><span class="new_code">submitFormById($id)</span></td>
<td>Submit a form without the submit value</td>
</tr>
                    <tr>
<td><span class="new_code">clickLink($label, $index)</span></td>
<td>Click an anchor by the visible label text</td>
</tr>
                    <tr>
<td><span class="new_code">clickLinkById($id)</span></td>
<td>Click an anchor by the ID attribute</td>
</tr>
                    <tr>
<td><span class="new_code">getFrameFocus()</span></td>
<td>The name of the currently selected frame</td>
</tr>
                    <tr>
<td><span class="new_code">setFrameFocusByIndex($choice)</span></td>
<td>Focus on a frame counting from 1</td>
</tr>
                    <tr>
<td><span class="new_code">setFrameFocus($name)</span></td>
<td>Focus on a frame by name</td>
</tr>
                </tbody></table>
            </p>
            <p>
                The parameters in the <span class="new_code">get()</span>, <span class="new_code">post()</span> or
                <span class="new_code">head()</span> methods are optional.
                The HTTP HEAD fetch does not change the browser context, only loads
                cookies.
                This can be useful for when an image or stylesheet sets a cookie
                for crafty robot blocking.
            </p>
            <p>
                The <span class="new_code">retry()</span>, <span class="new_code">back()</span> and
                <span class="new_code">forward()</span> commands work as they would on
                your web browser.
                They use the history to retry pages.
                This can be handy for checking the effect of hitting the
                back button on your forms.
            </p>
            <p>
                The frame methods need a little explanation.
                By default a framed page is treated just like any other.
                Content will be searced for throughout the entire frameset,
                so clicking a link will work no matter which frame
                the anchor tag is in.
                You can override this behaviour by focusing on a single
                frame.
                If you do that, all searches and actions will apply to that
                frame alone, such as authentication and retries.
                If a link or button is not in a focused frame then it cannot
                be clicked.
            </p>
            <p>
                Testing navigation on fixed pages only tells you when you
                have broken an entire script.
                For highly dynamic pages, such as for bulletin boards, this can
                be crucial for verifying the correctness of the application.
                For most applications though, the really tricky logic is usually in
                the handling of forms and sessions.
                Fortunately SimpleTest includes
                <a href="form_testing_documentation.html">tools for testing web forms</a>
                as well.
            </p>
        
        <p><a class="target" name="request"><h2>Modifying the request</h2></a></p>
            <p>
                Although SimpleTest does not have the goal of testing networking
                problems, it does include some methods to modify and debug
                the requests it makes.
                Here is another method list...
                <table><tbody>
                    <tr>
<td><span class="new_code">getTransportError()</span></td>
<td>The last socket error</td>
</tr>
                    <tr>
<td><span class="new_code">showRequest()</span></td>
<td>Dump the outgoing request</td>
</tr>
                    <tr>
<td><span class="new_code">showHeaders()</span></td>
<td>Dump the incoming headers</td>
</tr>
                    <tr>
<td><span class="new_code">showSource()</span></td>
<td>Dump the raw HTML page content</td>
</tr>
                    <tr>
<td><span class="new_code">ignoreFrames()</span></td>
<td>Do not load framesets</td>
</tr>
                    <tr>
<td><span class="new_code">setCookie($name, $value)</span></td>
<td>Set a cookie from now on</td>
</tr>
                    <tr>
<td><span class="new_code">addHeader($header)</span></td>
<td>Always add this header to the request</td>
</tr>
                    <tr>
<td><span class="new_code">setMaximumRedirects($max)</span></td>
<td>Stop after this many redirects</td>
</tr>
                    <tr>
<td><span class="new_code">setConnectionTimeout($timeout)</span></td>
<td>Kill the connection after this time between bytes</td>
</tr>
                    <tr>
<td><span class="new_code">useProxy($proxy, $name, $password)</span></td>
<td>Make requests via this proxy URL</td>
</tr>
                </tbody></table>
                These methods are principally for debugging.
            </p>
        
    </div>
        References and related information...
        <ul>
<li>
            SimpleTest project page on <a href="http://sourceforge.net/projects/simpletest/">SourceForge</a>.
        </li>
<li>
            SimpleTest download page on <a href="http://www.lastcraft.com/simple_test.php">LastCraft</a>.
        </li>
<li>
            The <a href="http://simpletest.org/api/">developer's API for SimpleTest</a>
            gives full detail on the classes and assertions available.
        </li>
</ul>
<div class="menu_back"><div class="menu">
<a href="index.html">SimpleTest</a>
                |
                <a href="overview.html">Overview</a>
                |
                <a href="unit_test_documentation.html">Unit tester</a>
                |
                <a href="group_test_documentation.html">Group tests</a>
                |
                <a href="mock_objects_documentation.html">Mock objects</a>
                |
                <a href="partial_mocks_documentation.html">Partial mocks</a>
                |
                <a href="reporter_documentation.html">Reporting</a>
                |
                <a href="expectation_documentation.html">Expectations</a>
                |
                <span class="chosen">Web tester</span>
                |
                <a href="form_testing_documentation.html">Testing forms</a>
                |
                <a href="authentication_documentation.html">Authentication</a>
                |
                <a href="browser_documentation.html">Scriptable browser</a>
</div></div>
<div class="copyright">
            Copyright<br>Marcus Baker 2006
        </div>
</body>
</html>
