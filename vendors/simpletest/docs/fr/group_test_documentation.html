<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Documentation SimpleTest : Grouper des tests</title>
<link rel="stylesheet" type="text/css" href="docs.css" title="Styles">
</head>
<body>
<div class="menu_back"><div class="menu">
<a href="index.html">SimpleTest</a>
                |
                <a href="overview.html">Overview</a>
                |
                <a href="unit_test_documentation.html">Unit tester</a>
                |
                <a href="group_test_documentation.html">Group tests</a>
                |
                <a href="mock_objects_documentation.html">Mock objects</a>
                |
                <a href="partial_mocks_documentation.html">Partial mocks</a>
                |
                <a href="reporter_documentation.html">Reporting</a>
                |
                <a href="expectation_documentation.html">Expectations</a>
                |
                <a href="web_tester_documentation.html">Web tester</a>
                |
                <a href="form_testing_documentation.html">Testing forms</a>
                |
                <a href="authentication_documentation.html">Authentication</a>
                |
                <a href="browser_documentation.html">Scriptable browser</a>
</div></div>
<h1>Documentation sur le groupement des tests</h1>
        This page...
        <ul>
<li>
            Plusieurs approches pour <a href="#group">grouper des tests</a> ensemble.
        </li>
<li>
            Combiner des groupes des tests dans des
            <a href="#plus-haut">groupes plus grands</a>.
        </li>
<li>
            Intégrer des <a href="#heritage">scénarios de test hérités</a>
            d'un autre type de PHPUnit.
        </li>
</ul>
<div class="content">
        <p><a class="target" name="grouper"><h2>Grouper des tests</h2></a></p>
            <p>
                Pour lancer les scénarios de tests en tant que groupe,
                ils devraient être placés dans des fichiers sans le code du lanceur...
<pre>
<strong>&lt;?php
    require_once('../classes/io.php');

    class FileTester extends UnitTestCase {
        ...
    }

    class SocketTester extends UnitTestCase {
        ...
    }
?&gt;</strong>
</pre>
                Autant de scénarios que nécessaires peuvent être
                mis dans un fichier unique. Ils doivent contenir
                tout le code nécessaire, entre autres la bibliothèque testée,
                mais aucune des bibliothèques de SimpleTest.
            </p>
            <p>
                Si vous avez étendu l'un ou l'autre des scénarios de test,
                vous pouvez aussi les inclure.
<pre>
&lt;?php
    require_once('../classes/io.php');
<strong>
    class MyFileTestCase extends UnitTestCase {
        ...
    }
    SimpleTestOptions::ignore('MyFileTestCase');</strong>

    class FileTester extends MyFileTestCase {
        ...
    }

    class SocketTester extends UnitTestCase {
        ...
    }
?&gt;
</pre>
                La classe <span class="new_code">FileTester</span> ne contient aucun test véritable,
                il s'agit d'une classe de base pour d'autres scénarios de test.
                Pour cette raison nous utilisons la directive
                <span class="new_code">SimpleTestOptions::ignore()</span> pour indiquer
                au prochain groupe de tests de l'ignorer.
                Cette directive peut se placer n'importe où dans le fichier
                et fonctionne quand un fichier complet des scénarios de test
                est chargé (cf. ci-dessous).
                Nous l'appelons <em>file_test.php</em>.
            </p>
            <p>
                Ensuite nous créons un fichier de groupe de tests,
                disons <em>group_test.php</em>.
                Vous penserez à un nom plus convaincant, j'en suis sûr.
                Nous lui ajoutons le fichier de test avec une méthode sans risque...
<pre>
&lt;?php
    require_once('simpletest/unit_tester.php');
    require_once('simpletest/reporter.php');<strong>
    require_once('file_test.php');

    $test = &amp;new GroupTest('All file tests');
    $test-&gt;addTestCase(new FileTestCase());
    $test-&gt;run(new HtmlReporter());</strong>
?&gt;
</pre>
                Ceci instancie le scénario de test avant que
                la suite de test ne soit lancée.
                Ça pourrait devenir assez onéreux avec
                un grand nombre de scénarios de test : 
                il existe donc une autre méthode qui instancie
                la classe uniquement quand elle devient nécessaire...
<pre>
&lt;?php
    require_once('simpletest/unit_tester.php');
    require_once('simpletest/reporter.php');
    require_once('file_test.php');

    $test = &amp;new GroupTest('All file tests');<strong>
    $test-&gt;addTestClass('FileTestCase');</strong>
    $test-&gt;run(new HtmlReporter());
?&gt;
</pre>
                Le problème de cette technique est que pour
                chaque scénario de test supplémentaire nous aurons à importer
                (via <span class="new_code">require_once()</span>) le fichier de code de test
                et à instancier manuellement chaque scénario de test.
                Nous pouvons nous épargner beaucoup de dactylographie avec...
<pre>
&lt;?php
    require_once('simpletest/unit_tester.php');
    require_once('simpletest/reporter.php');

    $test = &amp;new GroupTest('All file tests');<strong>
    $test-&gt;addTestFile('file_test.php');</strong>
    $test-&gt;run(new HtmlReporter());
?&gt;
</pre>
                Voici ce qui vient de se passer :
                la classe <span class="new_code">GroupTest</span> a réalisé le
                <span class="new_code">require_once()</span> pour nous.
                Ensuite elle vérifie si de nouvelles classes de scénario
                de test ont été créées par ce nouveau fichier
                et les ajoute automatiquement au groupe de tests.
                Désormais tout ce qu'il nous reste à faire,
                c'est d'ajouter chaque nouveau fichier.
            </p>
            <p>
                Il y a deux choses qui peuvent planter
                et qui demandent un minimum d'attention...
                <ol>
                    <li>
                        Le fichier peut déjà avoir été analysé par PHP
                        et dans ce cas aucune classe ne sera ajoutée.
                        Pensez à bien vérifier que les scénarios de test
                        ne sont inclus que dans ce fichier et dans aucun autre
                        (Note : avec la nouvelle fonctionnalité <cite>autorun</cite>,
                        ce problème a maintenant été résolu).
                    </li>
                    <li>
                        Les nouvelles classes d'extension de scénario
                        de test qui sont incluses seront placées
                        dans le groupe de tests et exécutées par la même occasion.
                        Vous aurez à ajouter une directive
                        <span class="new_code">SimpleTestOptions::ignore()</span> pour ces classes
                        ou alors pensez à les ajouter avant la ligne
                        <span class="new_code">GroupTest::addTestFile()</span>.
                    </li>
                </ol>
            </p>
        
        <p><a class="target" name="plus-haut"><h2>Groupements de plus haut niveau</h2></a></p>
            <p>
                La technique ci-dessus place tous les scénarios de test
                dans un unique et grand groupe.
                Sauf que pour des projets plus conséquents,
                ce n'est probablement pas assez souple;
                vous voudriez peut-être grouper les tests tout à fait différemment.
            </p>
            <p>
                Pour obtenir un groupe de tests plus souple
                nous pouvons sous classer <span class="new_code">GroupTest</span>
                et ensuite l'instancier au cas par cas...
<pre>
&lt;?php
    require_once('simpletest/unit_tester.php');
    require_once('simpletest/reporter.php');
    <strong>
    class FileGroupTest extends GroupTest {
        function FileGroupTest() {
            $this-&gt;GroupTest('All file tests');
            $this-&gt;addTestFile('file_test.php');
        }
    }</strong>
?&gt;
</pre>
                Ceci nomme le test dans le constructeur
                et ensuite ajoute à la fois nos scénarios
                de test et un unique groupe en dessous.
                Bien sûr nous pouvons ajouter plus d'un groupe à cet instant.
                Nous pouvons maintenant invoquer les tests
                à partir d'un autre fichier d'exécution...
<pre>
&lt;?php
    require_once('file_group_test.php');
    <strong>
    $test = &amp;new FileGroupTest();
    $test-&gt;run(new HtmlReporter());</strong>
?&gt;
</pre>
                ...ou alors nous pouvons les grouper
                dans un groupe de tests encore plus grand...
<pre>
&lt;?php
    require_once('file_group_test.php');
    <strong>
    $test = &amp;new BigGroupTest('Big group');
    $test-&gt;addTestCase(new FileGroupTest());
    $test-&gt;addTestCase(...);
    $test-&gt;run(new HtmlReporter());</strong>
?&gt;
</pre>
                Si nous souhaitons lancer le groupe de tests original
                sans utiliser ses petits fichiers d'exécution,
                nous pouvons mettre le code du lanceur de test
                derrière des barreaux quand nous créons chaque groupe.
<pre>
&lt;?php
    class FileGroupTest extends GroupTest {
        function FileGroupTest() {
            $this-&gt;GroupTest('All file tests');
            $test-&gt;addTestFile('file_test.php');
        }
    }
    <strong>
    if (! defined('RUNNER')) {
        define('RUNNER', true);</strong>
        $test = &amp;new FileGroupTest();
        $test-&gt;run(new HtmlReporter());
    }
?&gt;
</pre>
                Cette approche exige aux barrières d'être activées
                à l'inclusion du fichier de groupe de tests,
                mais c'est quand même moins de tracas que beaucoup
                de fichiers de lancement éparpillés.
                Reste à inclure des barreaux identiques
                au niveau supérieur afin de s'assurer que
                le <span class="new_code">run()</span> ne sera lancé qu'une seule fois
                à partir du script de haut niveau qui l'a invoqué.
<pre>
&lt;?php
    define('RUNNER', true);

    require_once('file_group_test.php');
    $test = &amp;new BigGroupTest('Big group');
    $test-&gt;addTestCase(new FileGroupTest());
    $test-&gt;addTestCase(...);
    $test-&gt;run(new HtmlReporter());
?&gt;
</pre>
                Comme les scénarios de test normaux,
                un <span class="new_code">GroupTest</span> peut être chargé avec la méthode
                <span class="new_code">GroupTest::addTestFile()</span>.
<pre>  
&lt;?php   
    define('RUNNER', true); 
        
    $test = &amp;new BigGroupTest('Big group');<strong> 
    $test-&gt;addTestFile('file_group_test.php');  
    $test-&gt;addTestFile(...);</strong>   
    $test-&gt;run(new HtmlReporter()); 
?&gt;  
</pre>
            </p>
        
        <p><a class="target" name="heritage"><h2>Intégrer des scénarios de test hérités</h2></a></p>
            <p>
                Si vous avez déjà des tests unitaires pour votre code
                ou alors si vous étendez des classes externes
                qui ont déjà leurs propres tests, il y a peu de chances
                pour que ceux-ci soient déjà au format SimpleTest.
                Heureusement il est possible d'incorporer ces scénarios
                de test en provenance d'autres testeurs unitaires
                directement dans des groupes de test SimpleTest.
            </p>
            <p>
                Par exemple, supposons que nous ayons
                ce scénario de test prévu pour
                <a href="http://sourceforge.net/projects/phpunit">PhpUnit</a>
                dans le fichier <em>config_test.php</em>...
<pre>
<strong>class ConfigFileTest extends TestCase {
    function ConfigFileTest() {
        $this-&gt;TestCase('Config file test');
    }
    
    function testContents() {
        $config = new ConfigFile('test.conf');
        $this-&gt;assertRegexp('/me/', $config-&gt;getValue('username'));
    }
}</strong>
</pre>
                Le groupe de tests peut le reconnaître à partir
                du moment où nous mettons l'adaptateur approprié
                avant d'ajouter le fichier de test...
<pre>
&lt;?php
    require_once('simpletest/unit_tester.php');
    require_once('simpletest/reporter.php');<strong>
    require_once('simpletest/adapters/phpunit_test_case.php');</strong>

    $test = &amp;new GroupTest('All file tests');<strong>
    $test-&gt;addTestFile('config_test.php');</strong>
    $test-&gt;run(new HtmlReporter());
?&gt;
</pre>
                Il n'y a que deux adaptateurs,
                l'autre est pour le paquet testeur unitaire de
                <a href="http://pear.php.net/manual/en/package.php.phpunit.php">PEAR</a>...
<pre>
&lt;?php
    require_once('simpletest/unit_tester.php');
    require_once('simpletest/reporter.php');<strong>
    require_once('simpletest/adapters/pear_test_case.php');</strong>

    $test = &amp;new GroupTest('All file tests');<strong>
    $test-&gt;addTestFile('some_pear_test_cases.php');</strong>
    $test-&gt;run(new HtmlReporter());
?&gt;
</pre>
                Les scénarios de test de PEAR peuvent être
                librement mélangés avec ceux de SimpleTest
                mais vous ne pouvez pas utiliser les assertions
                de SimpleTest au sein des versions héritées
                des scénarios de test. La raison ?
                Une simple vérification que vous ne rendez pas
                par accident vos scénarios de test complètement
                dépendants de SimpleTest.
                Peut-être que vous souhaitez publier
                votre bibliothèque sur PEAR par exemple :
                ça voudrait dire la livrer avec des scénarios de
                test compatibles avec PEAR::PhpUnit.
            </p>
        
    </div>
        References and related information...
        <ul>
<li>
            La page du projet SimpleTest sur
            <a href="http://sourceforge.net/projects/simpletest/">SourceForge</a>.
        </li>
<li>
            La page de téléchargement de SimpleTest sur
            <a href="http://www.lastcraft.com/simple_test.php">LastCraft</a>.
        </li>
</ul>
<div class="menu_back"><div class="menu">
<a href="index.html">SimpleTest</a>
                |
                <a href="overview.html">Overview</a>
                |
                <a href="unit_test_documentation.html">Unit tester</a>
                |
                <a href="group_test_documentation.html">Group tests</a>
                |
                <a href="mock_objects_documentation.html">Mock objects</a>
                |
                <a href="partial_mocks_documentation.html">Partial mocks</a>
                |
                <a href="reporter_documentation.html">Reporting</a>
                |
                <a href="expectation_documentation.html">Expectations</a>
                |
                <a href="web_tester_documentation.html">Web tester</a>
                |
                <a href="form_testing_documentation.html">Testing forms</a>
                |
                <a href="authentication_documentation.html">Authentication</a>
                |
                <a href="browser_documentation.html">Scriptable browser</a>
</div></div>
<div class="copyright">
            Copyright<br>Marcus Baker 2006
        </div>
</body>
</html>
