rm -f application.zip

git archive -v -o application.zip --format=zip HEAD

zip -rv application.zip app/views/layouts/prod/*
zip -rv application.zip app/webroot/js/site/*
zip -rv application.zip app/webroot/js/build
zip -rv application.zip app/webroot/css/build
# zip -rv application.zip app/webroot/css/critical # disabled for now, critical not actually being used
zip -rv application.zip app/webroot/css/screen.css
zip -rv application.zip app/webroot/css/maps
zip -rv application.zip app/webroot/img/generated
zip -rv application.zip app/webroot/bower_components/modernizr/modernizr_build.js

# eb deploy Bvwww-env-1

# lftp -e "set ssl:verify-certificate no; mirror /www.bon-voyage.co.uk/app/webroot/img/uploads/originals /resources/images/originals --dry-run" -u moz,B0nv0y4g3 ************
# lftp -e "set ssl:verify-certificate no; mirror /www.bon-voyage.co.uk/app/webroot/files/uploads /resources/files --dry-run" -u moz,B0nv0y4g3 ************
# lftp -e "set ssl:verify-certificate no; mirror /www.bon-voyage.co.uk/app/webroot/blog/app/uploads /resources/wordpress --dry-run" -u moz,B0nv0y4g3 ************

# aws s3 sync s3://bv-www-resources/ /resources
